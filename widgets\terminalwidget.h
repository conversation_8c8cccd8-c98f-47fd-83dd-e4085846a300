#ifndef TERMINALWIDGET_H
#define TERMINALWIDGET_H

#include <QWidget>
#include <QTextEdit>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QComboBox>
#include <QLabel>
#include <QCheckBox>
#include <QSpinBox>

/**
 * @brief 交互式终端控件
 * 
 * 提供串口监视器和REPL交互功能
 * 实现PRD中FR-4的需求：
 * - FR-4.1: 串口监视器
 * - FR-4.2: REPL交互
 */
class TerminalWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 终端模式枚举
     */
    enum TerminalMode {
        Monitor,    ///< 监视器模式（只读）
        REPL       ///< REPL交互模式
    };

    explicit TerminalWidget(QWidget *parent = nullptr);
    ~TerminalWidget();

    /**
     * @brief 设置终端模式
     * @param mode 终端模式
     */
    void setTerminalMode(TerminalMode mode);

    /**
     * @brief 获取当前终端模式
     * @return 终端模式
     */
    TerminalMode getTerminalMode() const;

    /**
     * @brief 追加文本到终端
     * @param text 要追加的文本
     */
    void appendText(const QString &text);

    /**
     * @brief 追加带颜色的文本到终端
     * @param text 要追加的文本
     * @param color 文本颜色
     */
    void appendColoredText(const QString &text, const QColor &color);

    /**
     * @brief 清空终端内容
     */
    void clearTerminal();

    /**
     * @brief 设置是否自动滚动
     * @param autoScroll 是否自动滚动
     */
    void setAutoScroll(bool autoScroll);

    /**
     * @brief 获取是否自动滚动
     * @return 是否自动滚动
     */
    bool getAutoScroll() const;

    /**
     * @brief 设置是否显示时间戳
     * @param showTimestamp 是否显示时间戳
     */
    void setShowTimestamp(bool showTimestamp);

    /**
     * @brief 获取是否显示时间戳
     * @return 是否显示时间戳
     */
    bool getShowTimestamp() const;

    /**
     * @brief 设置最大行数
     * @param maxLines 最大行数，0表示无限制
     */
    void setMaxLines(int maxLines);

    /**
     * @brief 获取最大行数
     * @return 最大行数
     */
    int getMaxLines() const;

    /**
     * @brief 设置字体
     * @param font 字体
     */
    void setTerminalFont(const QFont &font);

    /**
     * @brief 获取字体
     * @return 字体
     */
    QFont getTerminalFont() const;

    /**
     * @brief 保存终端内容到文件
     * @param filePath 文件路径
     * @return 保存是否成功
     */
    bool saveToFile(const QString &filePath);

    /**
     * @brief 获取终端内容
     * @return 终端内容
     */
    QString getTerminalContent() const;

    /**
     * @brief 设置输入提示符
     * @param prompt 提示符
     */
    void setPrompt(const QString &prompt);

    /**
     * @brief 获取输入提示符
     * @return 提示符
     */
    QString getPrompt() const;

signals:
    /**
     * @brief 用户输入命令时发出
     * @param command 输入的命令
     */
    void commandEntered(const QString &command);

    /**
     * @brief 终端模式发生变化时发出
     * @param mode 新的终端模式
     */
    void terminalModeChanged(TerminalMode mode);

    /**
     * @brief 请求清空终端时发出
     */
    void clearRequested();

    /**
     * @brief 请求保存终端内容时发出
     */
    void saveRequested();

private slots:
    /**
     * @brief 处理输入框回车事件
     */
    void handleInputReturn();

    /**
     * @brief 处理清空按钮点击
     */
    void handleClearClicked();

    /**
     * @brief 处理保存按钮点击
     */
    void handleSaveClicked();

    /**
     * @brief 处理自动滚动复选框状态变化
     * @param checked 是否选中
     */
    void handleAutoScrollChanged(bool checked);

    /**
     * @brief 处理时间戳复选框状态变化
     * @param checked 是否选中
     */
    void handleTimestampChanged(bool checked);

    /**
     * @brief 处理最大行数变化
     * @param maxLines 最大行数
     */
    void handleMaxLinesChanged(int maxLines);

    /**
     * @brief 处理终端模式变化
     * @param index 模式索引
     */
    void handleModeChanged(int index);

private:
    /**
     * @brief 初始化UI
     */
    void initializeUI();

    /**
     * @brief 创建工具栏
     */
    void createToolBar();

    /**
     * @brief 更新UI状态
     */
    void updateUIState();

    /**
     * @brief 限制终端行数
     */
    void limitTerminalLines();

    /**
     * @brief 获取当前时间戳
     * @return 时间戳字符串
     */
    QString getCurrentTimestamp() const;

    /**
     * @brief 滚动到底部
     */
    void scrollToBottom();

private:
    // UI组件
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_toolBarLayout;
    QTextEdit *m_terminalDisplay;
    QLineEdit *m_inputLine;
    QPushButton *m_clearButton;
    QPushButton *m_saveButton;
    QComboBox *m_modeComboBox;
    QCheckBox *m_autoScrollCheckBox;
    QCheckBox *m_timestampCheckBox;
    QSpinBox *m_maxLinesSpinBox;
    QLabel *m_promptLabel;

    // 状态变量
    TerminalMode m_currentMode;
    bool m_autoScroll;
    bool m_showTimestamp;
    int m_maxLines;
    QString m_prompt;
    int m_currentLineCount;
};

#endif // TERMINALWIDGET_H
