#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLabel>
#include <QProgressBar>
#include <QTimer>
#include <QFileDialog>
#include <QMessageBox>

// 核心模块
#include "core/blocklymanager.h"
#include "core/serialmanager.h"
#include "core/codedeploymanager.h"
#include "core/firmwaremanager.h"
#include "core/projectmanager.h"
#include "widgets/terminalwidget.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

/**
 * @brief 主窗口类
 *
 * 集成所有功能模块，提供统一的用户界面
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    // 文件菜单
    void onNewProject();
    void onOpenProject();
    void onSaveProject();
    void onSaveProjectAs();
    void onExportCode();
    void onExit();

    // 编辑菜单
    void onUndo();
    void onRedo();
    void onClearWorkspace();

    // 工具菜单
    void onFirmwareManager();
    void onSerialMonitor();
    void onSettings();

    // 帮助菜单
    void onAbout();
    void onDocumentation();

    // 连接控制
    void onRefreshPorts();
    void onConnect();
    void onDisconnect();

    // 代码部署
    void onRunCode();
    void onStopCode();

    // 固件管理
    void onCheckUpdate();
    void onFlashFirmware();

    // 模块信号处理
    void onBlocklyCodeChanged(const QString &code);
    void onSerialConnectionStateChanged(SerialManager::ConnectionState state);
    void onDeployStateChanged(CodeDeployManager::DeployState state);
    void onProjectModifiedChanged(bool modified);

private:
    /**
     * @brief 初始化UI组件
     */
    void initializeUI();

    /**
     * @brief 初始化所有管理器
     */
    void initializeManagers();

    /**
     * @brief 连接信号和槽
     */
    void connectSignals();

    /**
     * @brief 更新UI状态
     */
    void updateUIState();

    /**
     * @brief 更新窗口标题
     */
    void updateWindowTitle();

    /**
     * @brief 更新状态栏
     */
    void updateStatusBar();

    /**
     * @brief 检查项目是否已修改，询问是否保存
     * @return 是否可以继续操作
     */
    bool checkSaveProject();

private:
    Ui::MainWindow *ui;

    // 核心管理器
    BlocklyManager *m_blocklyManager;
    SerialManager *m_serialManager;
    CodeDeployManager *m_codeDeployManager;
    FirmwareManager *m_firmwareManager;
    ProjectManager *m_projectManager;

    // UI组件
    TerminalWidget *m_terminalWidget;
    QLabel *m_connectionStatusLabel;
    QProgressBar *m_progressBar;

    // 状态变量
    bool m_isConnected;
    QString m_currentCode;
};

#endif // MAINWINDOW_H
