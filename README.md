# ESP32 Blockly Studio

ESP32 Blockly Studio是一个基于Qt和Google Blockly的可视化编程环境，专为ESP32微控制器开发设计。

## 功能特性

### 核心功能
- **可视化编程**: 基于Google Blockly的拖拽式编程界面
- **实时代码预览**: 自动生成MicroPython代码并实时显示
- **串口通信**: 自动检测ESP32设备，支持串口监视器和REPL交互
- **一键部署**: 集成mpremote工具，支持代码一键部署到ESP32
- **固件管理**: 支持MicroPython固件的检查、下载和烧录
- **项目管理**: 完整的项目文件管理，支持保存、打开和导出

### 用户界面
- **分割式布局**: 左侧Blockly工作区，右侧代码预览和控制面板
- **交互式终端**: 底部终端支持串口监视器和REPL模式
- **状态栏**: 显示连接状态和操作进度
- **完整菜单**: 文件、编辑、工具和帮助菜单

## 技术架构

### 模块化设计
项目采用模块化架构，主要包含以下模块：

#### 核心模块 (core/)
- **BlocklyManager**: Blockly集成和代码生成
- **SerialManager**: ESP32串口通信管理
- **CodeDeployManager**: 代码部署管理
- **FirmwareManager**: 固件管理
- **ProjectManager**: 项目文件管理

#### UI组件 (widgets/)
- **TerminalWidget**: 交互式终端组件

#### 主窗口
- **MainWindow**: 主界面，集成所有功能模块

### 技术栈
- **Qt 5.14.2**: 跨平台GUI框架
- **QWebEngineView**: 嵌入Blockly Web界面
- **QSerialPort**: 串口通信
- **QNetworkAccessManager**: 网络请求（固件下载）
- **QProcess**: 外部工具集成（mpremote, esptool.py）
- **Google Blockly**: 可视化编程库
- **MicroPython**: 目标编程语言

## 编译和运行

### 环境要求
- Qt 5.14.2 或更高版本
- Qt WebEngine模块
- C++11 编译器
- Python 3.x（用于mpremote和esptool.py）

### 编译步骤
1. 确保Qt开发环境已正确安装
2. 打开Qt Creator或使用命令行
3. 编译项目：
   ```bash
   qmake esp32_Blockly_Studio.pro
   make
   ```

### 运行前准备
1. 安装Python依赖：
   ```bash
   pip install mpremote esptool
   ```
2. 确保ESP32设备驱动已安装
3. 连接ESP32设备到计算机

## 使用说明

### 基本工作流程
1. **创建项目**: 文件 → 新建项目
2. **可视化编程**: 在Blockly工作区拖拽积木块创建程序
3. **连接设备**: 选择串口并点击连接
4. **部署代码**: 点击"Run on ESP32"按钮
5. **监视输出**: 在终端查看程序运行结果

### ESP32积木块
项目提供了丰富的ESP32专用积木块：

#### GPIO控制
- 数字输出/输入
- 模拟读取
- 引脚模式设置

#### WiFi功能
- WiFi连接
- 网络状态检查

#### 传感器支持
- DHT22温湿度传感器
- 超声波传感器
- 触摸传感器

#### 显示功能
- OLED显示屏控制
- 文本显示

#### 通信功能
- 串口打印
- I2C通信

#### 时间控制
- 延时函数
- 时间获取

### 项目文件格式
项目文件使用`.esp32proj`扩展名，采用JSON格式存储：
- 项目信息（名称、作者、版本等）
- Blockly工作区XML
- 生成的代码
- 项目设置

## 开发说明

### 添加自定义积木块
1. 在`resources/blockly/esp32_blocks.js`中定义新的积木块
2. 实现对应的Python代码生成器
3. 在`resources/blockly/index.html`的工具箱中添加积木块

### 扩展功能模块
1. 在对应的管理器类中添加新功能
2. 在MainWindow中连接相关信号和槽
3. 更新UI界面以支持新功能

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系我们。
