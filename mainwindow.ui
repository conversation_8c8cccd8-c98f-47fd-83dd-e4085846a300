<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ESP32 Blockly Studio</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/app_icon.png</normaloff>:/icons/app_icon.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="spacing">
     <number>5</number>
    </property>
    <property name="leftMargin">
     <number>5</number>
    </property>
    <property name="topMargin">
     <number>5</number>
    </property>
    <property name="rightMargin">
     <number>5</number>
    </property>
    <property name="bottomMargin">
     <number>5</number>
    </property>
    <item>
     <widget class="QSplitter" name="mainSplitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QSplitter" name="leftSplitter">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>3</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <widget class="QWidget" name="blocklyWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>3</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="blocklyLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="blocklyLabel">
           <property name="text">
            <string>Blockly Workspace</string>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: #f0f0f0; padding: 5px; font-weight: bold;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QWebEngineView" name="blocklyWebView">
           <property name="url">
            <url>
             <string>about:blank</string>
            </url>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="terminalWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>1</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="terminalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="terminalLabel">
           <property name="text">
            <string>Terminal</string>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: #f0f0f0; padding: 5px; font-weight: bold;</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QSplitter" name="rightSplitter">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>1</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <widget class="QWidget" name="codePreviewWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>1</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="codePreviewLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="codePreviewLabel">
           <property name="text">
            <string>Code Preview</string>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: #f0f0f0; padding: 5px; font-weight: bold;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QTextEdit" name="codePreviewTextEdit">
           <property name="readOnly">
            <bool>true</bool>
           </property>
           <property name="font">
            <font>
             <family>Consolas</family>
             <pointsize>10</pointsize>
            </font>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="controlPanelWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>1</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="controlPanelLayout">
         <property name="spacing">
          <number>5</number>
         </property>
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="controlPanelLabel">
           <property name="text">
            <string>Control Panel</string>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: #f0f0f0; padding: 5px; font-weight: bold;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="connectionGroupBox">
           <property name="title">
            <string>Connection</string>
           </property>
           <layout class="QVBoxLayout" name="connectionLayout">
            <item>
             <layout class="QHBoxLayout" name="portLayout">
              <item>
               <widget class="QLabel" name="portLabel">
                <property name="text">
                 <string>Port:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="portComboBox"/>
              </item>
              <item>
               <widget class="QPushButton" name="refreshPortButton">
                <property name="text">
                 <string>Refresh</string>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>60</width>
                  <height>16777215</height>
                 </size>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="connectionButtonLayout">
              <item>
               <widget class="QPushButton" name="connectButton">
                <property name="text">
                 <string>Connect</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="disconnectButton">
                <property name="text">
                 <string>Disconnect</string>
                </property>
                <property name="enabled">
                 <bool>false</bool>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="deployGroupBox">
           <property name="title">
            <string>Deploy</string>
           </property>
           <layout class="QVBoxLayout" name="deployLayout">
            <item>
             <widget class="QPushButton" name="runButton">
              <property name="text">
               <string>Run on ESP32</string>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="stopButton">
              <property name="text">
               <string>Stop</string>
              </property>
              <property name="enabled">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="firmwareGroupBox">
           <property name="title">
            <string>Firmware</string>
           </property>
           <layout class="QVBoxLayout" name="firmwareLayout">
            <item>
             <widget class="QPushButton" name="checkUpdateButton">
              <property name="text">
               <string>Check Updates</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="flashFirmwareButton">
              <property name="text">
               <string>Flash Firmware</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1200</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionNew"/>
    <addaction name="actionOpen"/>
    <addaction name="actionSave"/>
    <addaction name="actionSaveAs"/>
    <addaction name="separator"/>
    <addaction name="actionExportCode"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuEdit">
    <property name="title">
     <string>Edit</string>
    </property>
    <addaction name="actionUndo"/>
    <addaction name="actionRedo"/>
    <addaction name="separator"/>
    <addaction name="actionClearWorkspace"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="title">
     <string>Tools</string>
    </property>
    <addaction name="actionFirmwareManager"/>
    <addaction name="actionSerialMonitor"/>
    <addaction name="separator"/>
    <addaction name="actionSettings"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionAbout"/>
    <addaction name="actionDocumentation"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuEdit"/>
   <addaction name="menuTools"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <widget class="QLabel" name="connectionStatusLabel">
    <property name="text">
     <string>Disconnected</string>
    </property>
   </widget>
   <widget class="QProgressBar" name="progressBar">
    <property name="visible">
     <bool>false</bool>
    </property>
   </widget>
  </widget>
  <action name="actionNew">
   <property name="text">
    <string>New Project</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionOpen">
   <property name="text">
    <string>Open Project</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="text">
    <string>Save Project</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="text">
    <string>Save Project As...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
  </action>
  <action name="actionExportCode">
   <property name="text">
    <string>Export Code...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+E</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
  </action>
  <action name="actionUndo">
   <property name="text">
    <string>Undo</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Z</string>
   </property>
  </action>
  <action name="actionRedo">
   <property name="text">
    <string>Redo</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Y</string>
   </property>
  </action>
  <action name="actionClearWorkspace">
   <property name="text">
    <string>Clear Workspace</string>
   </property>
  </action>
  <action name="actionFirmwareManager">
   <property name="text">
    <string>Firmware Manager</string>
   </property>
  </action>
  <action name="actionSerialMonitor">
   <property name="text">
    <string>Serial Monitor</string>
   </property>
  </action>
  <action name="actionSettings">
   <property name="text">
    <string>Settings</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About</string>
   </property>
  </action>
  <action name="actionDocumentation">
   <property name="text">
    <string>Documentation</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QWebEngineView</class>
   <extends>QWidget</extends>
   <header>qwebengineview.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
