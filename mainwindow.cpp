#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QCloseEvent>
#include <QSplitter>
#include <QInputDialog>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_blocklyManager(nullptr)
    , m_serialManager(nullptr)
    , m_codeDeployManager(nullptr)
    , m_firmwareManager(nullptr)
    , m_projectManager(nullptr)
    , m_terminalWidget(nullptr)
    , m_connectionStatusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_isConnected(false)
{
    ui->setupUi(this);

    initializeUI();
    initializeManagers();
    connectSignals();
    updateUIState();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (checkSaveProject()) {
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::initializeUI()
{
    // 设置窗口属性
    setWindowTitle("ESP32 Blockly Studio");
    resize(1200, 800);

    // 初始化状态栏组件
    m_connectionStatusLabel = new QLabel("Disconnected", this);
    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);

    ui->statusbar->addWidget(m_connectionStatusLabel);
    ui->statusbar->addPermanentWidget(m_progressBar);

    // 创建终端组件
    m_terminalWidget = new TerminalWidget(this);
    ui->terminalLayout->addWidget(m_terminalWidget);

    // 设置分割器比例
    ui->mainSplitter->setSizes({800, 400});
    ui->leftSplitter->setSizes({600, 200});
    ui->rightSplitter->setSizes({400, 400});
}

void MainWindow::initializeManagers()
{
    // 创建项目管理器
    m_projectManager = new ProjectManager(this);

    // 创建Blockly管理器
    m_blocklyManager = new BlocklyManager(this);

    // 创建串口管理器
    m_serialManager = new SerialManager(this);

    // 创建代码部署管理器
    m_codeDeployManager = new CodeDeployManager(this);

    // 创建固件管理器
    m_firmwareManager = new FirmwareManager(this);

    // 初始化Blockly
    m_blocklyManager->initialize(ui->blocklyTextEdit);
}

void MainWindow::connectSignals()
{
    // 文件菜单
    connect(ui->actionNew, &QAction::triggered, this, &MainWindow::onNewProject);
    connect(ui->actionOpen, &QAction::triggered, this, &MainWindow::onOpenProject);
    connect(ui->actionSave, &QAction::triggered, this, &MainWindow::onSaveProject);
    connect(ui->actionSaveAs, &QAction::triggered, this, &MainWindow::onSaveProjectAs);
    connect(ui->actionExportCode, &QAction::triggered, this, &MainWindow::onExportCode);
    connect(ui->actionExit, &QAction::triggered, this, &MainWindow::onExit);

    // 编辑菜单
    connect(ui->actionUndo, &QAction::triggered, this, &MainWindow::onUndo);
    connect(ui->actionRedo, &QAction::triggered, this, &MainWindow::onRedo);
    connect(ui->actionClearWorkspace, &QAction::triggered, this, &MainWindow::onClearWorkspace);

    // 工具菜单
    connect(ui->actionFirmwareManager, &QAction::triggered, this, &MainWindow::onFirmwareManager);
    connect(ui->actionSerialMonitor, &QAction::triggered, this, &MainWindow::onSerialMonitor);
    connect(ui->actionSettings, &QAction::triggered, this, &MainWindow::onSettings);

    // 帮助菜单
    connect(ui->actionAbout, &QAction::triggered, this, &MainWindow::onAbout);
    connect(ui->actionDocumentation, &QAction::triggered, this, &MainWindow::onDocumentation);

    // 控制面板按钮
    connect(ui->refreshPortButton, &QPushButton::clicked, this, &MainWindow::onRefreshPorts);
    connect(ui->connectButton, &QPushButton::clicked, this, &MainWindow::onConnect);
    connect(ui->disconnectButton, &QPushButton::clicked, this, &MainWindow::onDisconnect);
    connect(ui->runButton, &QPushButton::clicked, this, &MainWindow::onRunCode);
    connect(ui->stopButton, &QPushButton::clicked, this, &MainWindow::onStopCode);
    connect(ui->checkUpdateButton, &QPushButton::clicked, this, &MainWindow::onCheckUpdate);
    connect(ui->flashFirmwareButton, &QPushButton::clicked, this, &MainWindow::onFlashFirmware);

    // 管理器信号
    connect(m_blocklyManager, &BlocklyManager::codeChanged, this, &MainWindow::onBlocklyCodeChanged);
    connect(m_serialManager, &SerialManager::connectionStateChanged, this, &MainWindow::onSerialConnectionStateChanged);
    connect(m_codeDeployManager, &CodeDeployManager::deployStateChanged, this, &MainWindow::onDeployStateChanged);
    connect(m_projectManager, &ProjectManager::projectModifiedChanged, this, &MainWindow::onProjectModifiedChanged);

    // 终端信号
    connect(m_terminalWidget, &TerminalWidget::commandEntered, m_serialManager, &SerialManager::sendData);
    connect(m_serialManager, &SerialManager::dataReceived, m_terminalWidget, &TerminalWidget::appendText);
}

// 文件菜单槽函数
void MainWindow::onNewProject()
{
    if (!checkSaveProject()) {
        return;
    }

    bool ok;
    QString name = QInputDialog::getText(this, "New Project", "Project Name:", QLineEdit::Normal, "Untitled Project", &ok);
    if (ok && !name.isEmpty()) {
        m_projectManager->createNewProject(name, "", "");
        m_blocklyManager->clearWorkspace();
        updateWindowTitle();
    }
}

void MainWindow::onOpenProject()
{
    if (!checkSaveProject()) {
        return;
    }

    QString fileName = QFileDialog::getOpenFileName(this,
                                                   "Open Project",
                                                   "",
                                                   ProjectManager::getProjectFileFilter());
    if (!fileName.isEmpty()) {
        if (m_projectManager->openProject(fileName)) {
            QString xml = m_projectManager->getWorkspaceXml();
            m_blocklyManager->setWorkspaceXml(xml);
            updateWindowTitle();
        }
    }
}

void MainWindow::onSaveProject()
{
    if (!m_projectManager->hasOpenProject()) {
        return;
    }

    if (m_projectManager->getCurrentProjectPath().isEmpty()) {
        onSaveProjectAs();
    } else {
        m_projectManager->saveProject();
    }
}

void MainWindow::onSaveProjectAs()
{
    if (!m_projectManager->hasOpenProject()) {
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
                                                   "Save Project As",
                                                   "",
                                                   ProjectManager::getProjectFileFilter());
    if (!fileName.isEmpty()) {
        m_projectManager->saveProjectAs(fileName);
        updateWindowTitle();
    }
}

void MainWindow::onExportCode()
{
    QString fileName = QFileDialog::getSaveFileName(this,
                                                   "Export Code",
                                                   "main.py",
                                                   "Python Files (*.py);;All Files (*)");
    if (!fileName.isEmpty()) {
        m_projectManager->exportCode(fileName, m_currentCode);
    }
}

void MainWindow::onExit()
{
    close();
}

// 编辑菜单槽函数
void MainWindow::onUndo()
{
    m_blocklyManager->undo();
}

void MainWindow::onRedo()
{
    m_blocklyManager->redo();
}

void MainWindow::onClearWorkspace()
{
    int ret = QMessageBox::question(this, "Clear Workspace",
                                   "Are you sure you want to clear the workspace?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        m_blocklyManager->clearWorkspace();
    }
}

// 工具菜单槽函数
void MainWindow::onFirmwareManager()
{
    // TODO: 打开固件管理对话框
    QMessageBox::information(this, "Firmware Manager", "Firmware Manager dialog will be implemented here.");
}

void MainWindow::onSerialMonitor()
{
    // TODO: 打开串口监视器对话框
    QMessageBox::information(this, "Serial Monitor", "Serial Monitor dialog will be implemented here.");
}

void MainWindow::onSettings()
{
    // TODO: 打开设置对话框
    QMessageBox::information(this, "Settings", "Settings dialog will be implemented here.");
}

// 帮助菜单槽函数
void MainWindow::onAbout()
{
    QMessageBox::about(this, "About ESP32 Blockly Studio",
                      "ESP32 Blockly Studio v1.0\n\n"
                      "A visual programming environment for ESP32 microcontrollers.\n\n"
                      "Built with Qt and Google Blockly.");
}

void MainWindow::onDocumentation()
{
    // TODO: 打开文档
    QMessageBox::information(this, "Documentation", "Documentation will be available online.");
}

// 连接控制槽函数
void MainWindow::onRefreshPorts()
{
    ui->portComboBox->clear();
    QStringList ports = m_serialManager->getAvailablePorts();
    ui->portComboBox->addItems(ports);
}

void MainWindow::onConnect()
{
    QString port = ui->portComboBox->currentText();
    if (!port.isEmpty()) {
        m_serialManager->connectToDevice(port, 115200);
    }
}

void MainWindow::onDisconnect()
{
    m_serialManager->disconnectFromDevice();
}

// 代码部署槽函数
void MainWindow::onRunCode()
{
    if (!m_isConnected) {
        QMessageBox::warning(this, "Warning", "Please connect to ESP32 device first.");
        return;
    }

    if (m_currentCode.isEmpty()) {
        QMessageBox::warning(this, "Warning", "No code to deploy.");
        return;
    }

    QString port = ui->portComboBox->currentText();
    m_codeDeployManager->deployCode(m_currentCode, port);
}

void MainWindow::onStopCode()
{
    m_codeDeployManager->stopDeployment();
}

// 固件管理槽函数
void MainWindow::onCheckUpdate()
{
    m_firmwareManager->checkForUpdates();
}

void MainWindow::onFlashFirmware()
{
    QString port = ui->portComboBox->currentText();
    if (port.isEmpty()) {
        QMessageBox::warning(this, "Warning", "Please select a port first.");
        return;
    }

    int ret = QMessageBox::question(this, "Flash Firmware",
                                   "This will flash MicroPython firmware to your ESP32. Continue?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        m_firmwareManager->flashFirmware("", port);
    }
}

// 模块信号处理槽函数
void MainWindow::onBlocklyCodeChanged(const QString &code)
{
    m_currentCode = code;
    ui->codePreviewTextEdit->setPlainText(code);

    if (m_projectManager->hasOpenProject()) {
        m_projectManager->setGeneratedCode(code);
    }
}

void MainWindow::onSerialConnectionStateChanged(SerialManager::ConnectionState state)
{
    m_isConnected = (state == SerialManager::Connected);

    ui->connectButton->setEnabled(!m_isConnected);
    ui->disconnectButton->setEnabled(m_isConnected);
    ui->runButton->setEnabled(m_isConnected);

    updateStatusBar();
}

void MainWindow::onDeployStateChanged(CodeDeployManager::DeployState state)
{
    bool isDeploying = (state == CodeDeployManager::Deploying);

    ui->runButton->setEnabled(!isDeploying && m_isConnected);
    ui->stopButton->setEnabled(isDeploying);

    m_progressBar->setVisible(isDeploying);

    if (state == CodeDeployManager::DeploySuccess) {
        m_terminalWidget->appendColoredText("Code deployed successfully!", QColor(Qt::green));
    } else if (state == CodeDeployManager::DeployFailed) {
        m_terminalWidget->appendColoredText("Code deployment failed!", QColor(Qt::red));
    }
}

void MainWindow::onProjectModifiedChanged(bool modified)
{
    updateWindowTitle();
}

// 辅助函数实现
void MainWindow::updateUIState()
{
    // 初始化端口列表
    onRefreshPorts();

    // 更新菜单状态
    ui->actionSave->setEnabled(false);
    ui->actionSaveAs->setEnabled(false);
    ui->actionExportCode->setEnabled(false);
    ui->actionUndo->setEnabled(false);
    ui->actionRedo->setEnabled(false);

    updateStatusBar();
}

void MainWindow::updateWindowTitle()
{
    QString title = "ESP32 Blockly Studio";

    if (m_projectManager->hasOpenProject()) {
        ProjectManager::ProjectInfo info = m_projectManager->getCurrentProjectInfo();
        title += " - " + info.name;

        if (m_projectManager->isProjectModified()) {
            title += "*";
        }

        ui->actionSave->setEnabled(true);
        ui->actionSaveAs->setEnabled(true);
        ui->actionExportCode->setEnabled(true);
    } else {
        ui->actionSave->setEnabled(false);
        ui->actionSaveAs->setEnabled(false);
        ui->actionExportCode->setEnabled(false);
    }

    setWindowTitle(title);
}

void MainWindow::updateStatusBar()
{
    if (m_isConnected) {
        QString port = ui->portComboBox->currentText();
        m_connectionStatusLabel->setText(QString("Connected to %1").arg(port));
        m_connectionStatusLabel->setStyleSheet("color: green;");
    } else {
        m_connectionStatusLabel->setText("Disconnected");
        m_connectionStatusLabel->setStyleSheet("color: red;");
    }
}

bool MainWindow::checkSaveProject()
{
    if (!m_projectManager->hasOpenProject() || !m_projectManager->isProjectModified()) {
        return true;
    }

    int ret = QMessageBox::question(this, "Save Project",
                                   "The project has been modified. Do you want to save it?",
                                   QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel);

    if (ret == QMessageBox::Save) {
        onSaveProject();
        return !m_projectManager->isProjectModified(); // 如果保存失败，仍然是修改状态
    } else if (ret == QMessageBox::Discard) {
        return true;
    } else {
        return false; // Cancel
    }
}

