#ifndef FIRMWAREMANAGER_H
#define FIRMWAREMANAGER_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QProcess>
#include <QString>
#include <QJsonObject>

/**
 * @brief 固件管理器
 * 
 * 负责ESP32固件的检查、下载和烧录功能
 * 实现PRD中FR-3的所有需求：
 * - FR-3.1: 在线固件更新检查
 * - FR-3.2: 固件下载
 * - FR-3.3: 固件烧录
 */
class FirmwareManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 固件信息结构
     */
    struct FirmwareInfo {
        QString version;        ///< 版本号
        QString downloadUrl;    ///< 下载链接
        QString description;    ///< 描述信息
        QString releaseDate;    ///< 发布日期
        qint64 fileSize;       ///< 文件大小（字节）
        QString checksum;      ///< 校验和
    };

    /**
     * @brief 操作状态枚举
     */
    enum OperationState {
        Idle,               ///< 空闲状态
        CheckingUpdate,     ///< 检查更新中
        Downloading,        ///< 下载中
        Flashing,          ///< 烧录中
        Completed,         ///< 操作完成
        Failed             ///< 操作失败
    };

    explicit FirmwareManager(QObject *parent = nullptr);
    ~FirmwareManager();

    /**
     * @brief 检查固件更新
     * @param currentVersion 当前版本号，空字符串表示未知
     */
    void checkForUpdates(const QString &currentVersion = QString());

    /**
     * @brief 下载固件
     * @param firmwareInfo 固件信息
     * @param downloadPath 下载路径，空字符串使用默认路径
     * @return 下载是否开始成功
     */
    bool downloadFirmware(const FirmwareInfo &firmwareInfo, const QString &downloadPath = QString());

    /**
     * @brief 烧录固件
     * @param firmwarePath 固件文件路径
     * @param portName 串口名称
     * @param chipType ESP32芯片类型，默认"esp32"
     * @return 烧录是否开始成功
     */
    bool flashFirmware(const QString &firmwarePath, const QString &portName, const QString &chipType = "esp32");

    /**
     * @brief 停止当前操作
     */
    void stopCurrentOperation();

    /**
     * @brief 获取当前操作状态
     * @return 操作状态
     */
    OperationState getCurrentState() const;

    /**
     * @brief 检查是否正在执行操作
     * @return 是否正在执行操作
     */
    bool isBusy() const;

    /**
     * @brief 设置esptool工具路径
     * @param path esptool可执行文件路径
     */
    void setEsptoolPath(const QString &path);

    /**
     * @brief 获取esptool工具路径
     * @return esptool可执行文件路径
     */
    QString getEsptoolPath() const;

    /**
     * @brief 检查esptool工具是否可用
     * @return 工具是否可用
     */
    bool isEsptoolAvailable() const;

    /**
     * @brief 设置固件更新检查URL
     * @param url 更新检查URL
     */
    void setUpdateCheckUrl(const QString &url);

    /**
     * @brief 获取固件更新检查URL
     * @return 更新检查URL
     */
    QString getUpdateCheckUrl() const;

    /**
     * @brief 获取最新固件信息
     * @return 最新固件信息
     */
    FirmwareInfo getLatestFirmwareInfo() const;

    /**
     * @brief 擦除Flash
     * @param portName 串口名称
     * @return 擦除是否开始成功
     */
    bool eraseFlash(const QString &portName);

signals:
    /**
     * @brief 操作状态发生变化时发出
     * @param state 新的操作状态
     */
    void operationStateChanged(OperationState state);

    /**
     * @brief 检查更新完成时发出
     * @param hasUpdate 是否有更新
     * @param firmwareInfo 最新固件信息
     */
    void updateCheckFinished(bool hasUpdate, const FirmwareInfo &firmwareInfo);

    /**
     * @brief 下载进度更新时发出
     * @param bytesReceived 已接收字节数
     * @param bytesTotal 总字节数
     */
    void downloadProgressUpdated(qint64 bytesReceived, qint64 bytesTotal);

    /**
     * @brief 下载完成时发出
     * @param success 是否成功
     * @param filePath 下载的文件路径
     */
    void downloadFinished(bool success, const QString &filePath);

    /**
     * @brief 烧录进度更新时发出
     * @param progress 进度百分比 (0-100)
     * @param message 进度消息
     */
    void flashProgressUpdated(int progress, const QString &message);

    /**
     * @brief 烧录完成时发出
     * @param success 是否成功
     * @param message 完成消息
     */
    void flashFinished(bool success, const QString &message);

    /**
     * @brief 发生错误时发出
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private slots:
    /**
     * @brief 处理更新检查网络回复
     */
    void handleUpdateCheckReply();

    /**
     * @brief 处理固件下载网络回复
     */
    void handleDownloadReply();

    /**
     * @brief 处理下载进度
     * @param bytesReceived 已接收字节数
     * @param bytesTotal 总字节数
     */
    void handleDownloadProgress(qint64 bytesReceived, qint64 bytesTotal);

    /**
     * @brief 处理烧录进程完成
     * @param exitCode 退出代码
     * @param exitStatus 退出状态
     */
    void handleFlashProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);

    /**
     * @brief 处理烧录进程错误
     * @param error 进程错误类型
     */
    void handleFlashProcessError(QProcess::ProcessError error);

    /**
     * @brief 处理烧录进程输出
     */
    void handleFlashProcessOutput();

private:
    /**
     * @brief 更新操作状态
     * @param state 新状态
     */
    void updateOperationState(OperationState state);

    /**
     * @brief 解析固件信息JSON
     * @param json JSON对象
     * @return 固件信息
     */
    FirmwareInfo parseFirmwareInfo(const QJsonObject &json);

    /**
     * @brief 解析烧录进度
     * @param output 输出内容
     */
    void parseFlashProgress(const QString &output);

    /**
     * @brief 获取默认下载路径
     * @return 默认下载路径
     */
    QString getDefaultDownloadPath() const;

private:
    QNetworkAccessManager *m_networkManager;
    QNetworkReply *m_currentReply;
    QProcess *m_flashProcess;
    OperationState m_currentState;
    QString m_esptoolPath;
    QString m_updateCheckUrl;
    FirmwareInfo m_latestFirmwareInfo;
    QString m_downloadFilePath;
    int m_flashProgress;
};

#endif // FIRMWAREMANAGER_H
