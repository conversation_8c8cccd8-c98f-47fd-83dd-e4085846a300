#ifndef BLOCKLYMANAGER_H
#define BLOCKLYMANAGER_H

#include <QObject>
#include <QWebEngineView>
#include <QWebChannel>
#include <QString>
#include <QJsonObject>

/**
 * @brief Blockly编程核心管理器
 * 
 * 负责管理Google Blockly库的集成，提供积木工作区和代码生成功能
 * 实现PRD中FR-1的所有需求：
 * - FR-1.1: Blockly工作区（拖拽、缩放、撤销重做）
 * - FR-1.2: 积木块分类（基础逻辑、数学、文本、变量、函数、ESP32专属）
 * - FR-1.3: 代码实时预览
 */
class BlocklyManager : public QObject
{
    Q_OBJECT

public:
    explicit BlocklyManager(QObject *parent = nullptr);
    ~BlocklyManager();

    /**
     * @brief 初始化Blockly工作区
     * @param webView 用于显示Blockly的QWebEngineView
     * @return 初始化是否成功
     */
    bool initialize(QWebEngineView *webView);

    /**
     * @brief 获取当前工作区的XML表示
     * @return 工作区的XML字符串
     */
    QString getWorkspaceXml() const;

    /**
     * @brief 设置工作区内容
     * @param xml 工作区的XML字符串
     */
    void setWorkspaceXml(const QString &xml);

    /**
     * @brief 获取生成的MicroPython代码
     * @return 生成的代码字符串
     */
    QString getGeneratedCode() const;

    /**
     * @brief 清空工作区
     */
    void clearWorkspace();

    /**
     * @brief 撤销操作
     */
    void undo();

    /**
     * @brief 重做操作
     */
    void redo();

    /**
     * @brief 检查是否可以撤销
     * @return 是否可以撤销
     */
    bool canUndo() const;

    /**
     * @brief 检查是否可以重做
     * @return 是否可以重做
     */
    bool canRedo() const;

signals:
    /**
     * @brief 工作区内容发生变化时发出
     */
    void workspaceChanged();

    /**
     * @brief 生成的代码发生变化时发出
     * @param code 新生成的代码
     */
    void codeChanged(const QString &code);

    /**
     * @brief 撤销/重做状态发生变化时发出
     * @param canUndo 是否可以撤销
     * @param canRedo 是否可以重做
     */
    void undoRedoStateChanged(bool canUndo, bool canRedo);

private slots:
    /**
     * @brief 处理来自JavaScript的消息
     * @param message 消息内容
     */
    void handleJavaScriptMessage(const QJsonObject &message);

private:
    /**
     * @brief 加载Blockly HTML页面
     */
    void loadBlocklyPage();

    /**
     * @brief 设置Web通道用于JavaScript通信
     */
    void setupWebChannel();

    /**
     * @brief 注册自定义积木块
     */
    void registerCustomBlocks();

private:
    QWebEngineView *m_webView;
    QWebChannel *m_webChannel;
    QString m_currentCode;
    bool m_canUndo;
    bool m_canRedo;
    bool m_initialized;
};

#endif // BLOCKLYMANAGER_H
