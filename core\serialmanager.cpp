#include "serialmanager.h"
#include <QDebug>

SerialManager::SerialManager(QObject *parent)
    : QObject(parent)
    , m_serialPort(nullptr)
    , m_connectionState(Disconnected)
    , m_currentBaudRate(115200)
    , m_portDetectionTimer(new QTimer(this))
{
    // 初始化串口
    m_serialPort = new QSerialPort(this);
    
    // 连接信号
    connect(m_serialPort, &QSerialPort::readyRead, this, &SerialManager::handleDataReceived);
    connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::error),
            this, &SerialManager::handleSerialError);
    
    // 设置端口检测定时器
    connect(m_portDetectionTimer, &QTimer::timeout, this, &SerialManager::detectPortChanges);
    
    // 初始化可用端口列表
    m_lastAvailablePorts = getAvailablePorts();
}

SerialManager::~SerialManager()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
    }
}

QStringList SerialManager::getAvailablePorts() const
{
    QStringList portNames;
    const auto infos = QSerialPortInfo::availablePorts();
    
    for (const QSerialPortInfo &info : infos) {
        portNames << info.portName();
    }
    
    return portNames;
}

bool SerialManager::connectToPort(const QString &portName, int baudRate)
{
    if (m_connectionState == Connected || m_connectionState == Connecting) {
        qWarning() << "SerialManager: Already connected or connecting";
        return false;
    }

    updateConnectionState(Connecting);
    
    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(baudRate);
    m_serialPort->setDataBits(QSerialPort::Data8);
    m_serialPort->setParity(QSerialPort::NoParity);
    m_serialPort->setStopBits(QSerialPort::OneStop);
    m_serialPort->setFlowControl(QSerialPort::NoFlowControl);
    
    if (m_serialPort->open(QIODevice::ReadWrite)) {
        m_currentPortName = portName;
        m_currentBaudRate = baudRate;
        updateConnectionState(Connected);
        qDebug() << "SerialManager: Connected to" << portName << "at" << baudRate << "baud";
        return true;
    } else {
        updateConnectionState(Error);
        QString error = QString("Failed to connect to %1: %2").arg(portName, m_serialPort->errorString());
        emit errorOccurred(error);
        qWarning() << "SerialManager:" << error;
        return false;
    }
}

void SerialManager::disconnect()
{
    if (m_serialPort && m_serialPort->isOpen()) {
        m_serialPort->close();
        m_currentPortName.clear();
        updateConnectionState(Disconnected);
        qDebug() << "SerialManager: Disconnected";
    }
}

bool SerialManager::sendData(const QByteArray &data)
{
    if (!isConnected()) {
        qWarning() << "SerialManager: Not connected";
        return false;
    }

    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1) {
        emit errorOccurred("Failed to send data: " + m_serialPort->errorString());
        return false;
    }
    
    return m_serialPort->flush();
}

bool SerialManager::sendText(const QString &text)
{
    return sendData(text.toUtf8());
}

SerialManager::ConnectionState SerialManager::getConnectionState() const
{
    return m_connectionState;
}

QString SerialManager::getCurrentPortName() const
{
    return m_currentPortName;
}

int SerialManager::getCurrentBaudRate() const
{
    return m_currentBaudRate;
}

bool SerialManager::isConnected() const
{
    return m_connectionState == Connected && m_serialPort && m_serialPort->isOpen();
}

void SerialManager::startPortDetection(int interval)
{
    m_portDetectionTimer->start(interval);
}

void SerialManager::stopPortDetection()
{
    m_portDetectionTimer->stop();
}

void SerialManager::handleDataReceived()
{
    if (!m_serialPort) {
        return;
    }

    QByteArray data = m_serialPort->readAll();
    m_receiveBuffer.append(data);
    
    emit dataReceived(data);
    
    // 处理文本数据（按行分割）
    while (m_receiveBuffer.contains('\n')) {
        int index = m_receiveBuffer.indexOf('\n');
        QByteArray line = m_receiveBuffer.left(index);
        m_receiveBuffer.remove(0, index + 1);
        
        QString text = QString::fromUtf8(line).trimmed();
        if (!text.isEmpty()) {
            emit textReceived(text);
        }
    }
}

void SerialManager::handleSerialError(QSerialPort::SerialPortError error)
{
    if (error == QSerialPort::NoError) {
        return;
    }

    QString errorString;
    switch (error) {
    case QSerialPort::DeviceNotFoundError:
        errorString = "Device not found";
        break;
    case QSerialPort::PermissionError:
        errorString = "Permission denied";
        break;
    case QSerialPort::OpenError:
        errorString = "Failed to open device";
        break;
    case QSerialPort::WriteError:
        errorString = "Write error";
        break;
    case QSerialPort::ReadError:
        errorString = "Read error";
        break;
    case QSerialPort::ResourceError:
        errorString = "Resource error (device disconnected?)";
        break;
    case QSerialPort::UnsupportedOperationError:
        errorString = "Unsupported operation";
        break;
    case QSerialPort::TimeoutError:
        errorString = "Timeout error";
        break;
    default:
        errorString = "Unknown error";
        break;
    }

    updateConnectionState(Error);
    emit errorOccurred(errorString);
    qWarning() << "SerialManager: Serial error:" << errorString;
}

void SerialManager::detectPortChanges()
{
    QStringList currentPorts = getAvailablePorts();
    
    if (currentPorts != m_lastAvailablePorts) {
        m_lastAvailablePorts = currentPorts;
        emit availablePortsChanged(currentPorts);
    }
}

void SerialManager::updateConnectionState(ConnectionState state)
{
    if (m_connectionState != state) {
        m_connectionState = state;
        emit connectionStateChanged(state);
    }
}
