### **产品需求文档 (PRD): ESP32-Blockly Studio**

| 文档名称 | ESP32-Blockly Studio 产品需求文档 |
| :--- | :--- |
| **版本** | `V1.0` |
| **创建日期** | `2025-08-03` |
| **创建人** | lys |
| **状态** | `草案` |

---

### **1. 文档修订历史**

| 版本号 | 修订日期 | 修订人 | 修订内容 |
| :--- | :--- | :--- | :--- |
| V1.0 | 2025-08-03 | lys | 初始版本创建 |

---

### **2. 项目概述**

#### **2.1. 项目背景**
随着物联网 (IoT) 和创客教育的普及，ESP32 作为一款功能强大且性价比高的微控制器，受到了广大开发者和爱好者的青睐。然而，对于编程初学者、中小学师生而言，直接使用C/C++或MicroPython代码进行开发仍存在一定的学习门槛。

为了降低ESP32的学习和使用难度，我们计划开发一款名为 **ESP32-Blockly Studio** 的PC端桌面软件。该软件通过图形化的积木式编程，让用户以拖拽的方式完成逻辑设计，并能一键将程序下载到ESP32主板上运行，从而提供一个从创意到实现的一站式、低门槛开发环境。

#### **2.2. 产品目标**
*   **降低门槛**: 为编程初学者、教育者和爱好者提供一个直观、友好的ESP32图形化编程工具。
*   **整合工具链**: 将代码编写、编译、下载、调试和固件管理等分散的步骤整合到单一软件中，简化开发流程。
*   **提高效率**: 提供一键下载代码、一键烧录固件等功能，提升开发和调试效率。
*   **保证兼容性**: 软件支持主流的Windows 32位和64位操作系统，确保广泛的用户覆盖。

#### **2.3. 目标用户**
*   **中小学师生**: 用于STEM/创客教育课程，进行编程和物联网项目教学。
*   **电子爱好者/创客**: 快速验证硬件想法，制作创意项目原型。
*   **编程初学者**: 作为学习嵌入式系统和MicroPython的入门工具。

---

### **3. 功能性需求 (Functional Requirements)**

#### **FR-1: 图形化编程核心功能**
*   **FR-1.1: Blockly工作区**
    *   软件主界面需集成Google Blockly库，提供一个可拖拽、拼接积木的中心工作区域。
    *   工作区支持缩放、平移、对齐网格等基本操作。
    *   提供撤销/重做功能。
*   **FR-1.2: 积木块分类**
    *   左侧提供积木工具箱，按功能对积木进行分类，包括但不限于：
        *   **基础逻辑**: If-Else 判断、循环（For, While）、逻辑运算（与、或、非）。
        *   **数学**: 数字、基本运算符、随机数、映射。
        *   **文本**: 字符串拼接、转换。
        *   **变量**: 创建和使用变量。
        *   **函数**: 创建和调用自定义函数。
        *   **ESP32专属**:
            *   **引脚 (GPIO)**: 数字读/写、模拟读/写 (ADC/DAC)、PWM输出。
            *   **网络 (WiFi)**: 连接WiFi、创建热点。
            *   **传感器**: 板载传感器（如触摸、霍尔）以及常用外接传感器（如DHT11/22温湿度、超声波）的封装积木。
            *   **显示**: 驱动OLED/LCD屏幕显示文字和图形的积木。
            *   **通信**: 串口（UART）收发、I2C、SPI等。
            *   **时间**: 延时（毫秒/微秒）、获取系统时间。
*   **FR-1.3: 代码实时预览**
    *   界面需提供一个代码预览窗口，该窗口能实时显示当前积木块组合对应的MicroPython代码。
    *   用户对积木的任何修改（添加、删除、移动）都应即时反映在代码预览区。

#### **FR-2: 项目与代码管理**
*   **FR-2.1: 项目文件管理**
    *   支持新建、保存和打开项目文件。项目文件应保存当前Blockly工作区的状态。
*   **FR-2.2: 代码生成与导出**
    *   支持将生成的MicroPython代码一键复制到剪贴板。
    *   支持将生成的MicroPython代码导出为独立的 `.py` 文件。
*   **FR-2.3: 代码部署到ESP32**
    *   **FR-2.3.1: 串口连接**: 软件需能自动检测并列出当前电脑可用的COM端口。用户可以选择要连接的ESP32主板。
    *   **FR-2.3.2: 一键运行**: 提供一个“运行”或“下载到主板”按钮。点击后，软件后台调用`mpremote`工具，通过选定的串口将生成的MicroPython代码（通常命名为`main.py`）发送并保存到ESP32的文件系统中，然后自动重启主板以运行新代码。
    *   **FR-2.3.3: 部署状态反馈**: 在部署过程中，需向用户提供清晰的状态反馈，如“正在连接”、“正在发送代码”、“下载完成”等。

#### **FR-3: 固件管理**
*   **FR-3.1: 在线固件更新检查**
    *   软件内提供一个“检查更新”功能，用于检查ESP32的MicroPython最新稳定版固件。
    *   此功能需请求预设的服务器地址（如官方固件发布页的API或一个维护的JSON文件），获取最新固件版本号和下载链接。
    *   若有新版本，应提示用户。
*   **FR-3.2: 固件下载**
    *   用户确认后，软件可在后台下载固件文件（`.bin`格式）到本地指定或临时目录，并显示下载进度。
*   **FR-3.3: 固件烧录 (Flashing)**
    *   提供一个专门的固件烧录界面。
    *   用户可以选择已下载的固件文件（或手动选择本地的其他固件文件）。
    *   用户选择正确的COM端口。
    *   提供“开始烧录”按钮。点击前应有明确提示：“烧录固件将擦除主板上所有数据，是否继续？”
    *   点击后，软件后台调用`esptool.py`等烧录工具，执行擦除和烧录操作。
    *   烧录过程需提供实时的进度条和日志输出，方便用户了解状态和排查问题。

#### **FR-4: 交互式终端**
*   **FR-4.1: 串口监视器**
    *   内置一个串口终端工具。
    *   用户连接到ESP32后，可以在此终端查看到主板通过`print()`函数输出的信息。
    *   支持设置波特率、清空输出、自动滚动等功能。
*   **FR-4.2: REPL交互**
    *   终端支持向ESP32发送MicroPython命令，实现REPL（读取-求值-打印-循环）交互，方便用户进行快速调试。

---

### **4. 非功能性需求 (Non-Functional Requirements)**

| 类别 | 需求描述 |
| :--- | :--- |
| **性能** | - 软件启动时间在5秒以内。<br>- 拖拽积木、代码生成预览无明显延迟。<br>- 固件烧录和代码下载过程稳定，不应无故中断。 |
| **兼容性** | - **操作系统**: 完美兼容 Windows 7, 10, 11 的32位和64位版本。<br>- **硬件**: 兼容主流的ESP32开发板（如乐鑫官方DevKitC、NodeMCU-32S等）。 |
| **可用性** | - **安装**: 提供标准的 `.exe` 安装包，引导用户完成安装，并能创建桌面快捷方式。<br>- **界面**: UI布局清晰，图标和文字指引明确，核心功能按钮突出，易于查找。<br>- **反馈**: 关键操作（如连接、下载、烧录）必须有即时的、可视化的状态反馈（如提示文字、进度条、弹窗）。 |
| **稳定性** | - 软件长时间运行应保持稳定，无内存泄漏，不会频繁崩溃。 |
| **安全性** | - 在执行擦除/烧录等破坏性操作前，必须有二次确认弹窗，防止用户误操作。 |

---

### **5. 技术方案概要**

*   **开发框架**: 推荐使用 **Qt 5.14.2 (C++)**。
    *   **优势**: 性能高，跨平台（便于未来扩展到macOS/Linux），拥有强大的`QSerialPort`模块处理串口通信，以及`QWebEngineView`模块用于内嵌和交互Blockly（Web技术）。
*   **核心库集成**:
    *   **Blockly**: 以Web形式集成到Qt应用的`QWebEngineView`中，通过JavaScript与C++进行双向通信。
    *   **mpremote / esptool.py**: 将这两个工具（及其依赖的Python环境）作为资源打包进软件。Qt程序通过`QProcess`调用这些命令行工具，并捕获其输出，以实现代码部署和固件烧录。
*   **打包工具**:
    *   使用 **Inno Setup** 或 **NSIS** 创建 `.exe` 安装包。脚本需要配置好安装路径、桌面快捷方式、并将所有依赖（Qt的DLLs、Python环境、mpremote/esptool等）正确打包。

---

### **6. 版本规划 (Roadmap)**

*   **V1.0 (MVP - 最小可行产品)**
    *   实现FR-1（图形化编程核心）、FR-2（代码部署）和FR-4（串口监视器）。
    *   完成基础的UI框架和打包流程。
*   **V1.1**
    *   增加FR-3（固件管理）功能。
    *   优化UI/UX，修复V1.0的用户反馈问题。
*   **V1.2 及未来**
    *   增加更多传感器和模块的积木块。
    *   提供丰富的示例项目。
    *   软件多语言支持。
    *   考虑增加对其他主板（如ESP8266, ESP32-S3）的支持。