@echo off
echo ESP32 Blockly Studio Build Script
echo ==================================

REM 检查Qt环境
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: qmake not found in PATH
    echo Please make sure Qt is properly installed and added to PATH
    pause
    exit /b 1
)

REM 清理之前的构建文件
echo Cleaning previous build files...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release

REM 运行qmake
echo Running qmake...
qmake esp32_Blockly_Studio.pro
if %errorlevel% neq 0 (
    echo Error: qmake failed
    pause
    exit /b 1
)

REM 编译项目
echo Building project...
nmake
if %errorlevel% neq 0 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable should be in the debug or release folder
pause
