@echo off
echo ESP32 Blockly Studio Build Script
echo ==================================

REM 检查Qt环境
echo Checking Qt environment...
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: qmake not found in PATH
    echo Please install Qt and add it to PATH, or use Qt Creator
    echo.
    echo Alternative: Open esp32_Blockly_Studio.pro in Qt Creator
    pause
    exit /b 1
)

REM 检查编译器
echo Checking compiler...
where cl >nul 2>nul
if %errorlevel% neq 0 (
    where gcc >nul 2>nul
    if %errorlevel% neq 0 (
        echo Error: No C++ compiler found
        echo Please install Visual Studio Build Tools or MinGW
        echo Or use Qt Creator which includes compiler setup
        pause
        exit /b 1
    ) else (
        echo Found GCC compiler
        set COMPILER=mingw32-make
    )
) else (
    echo Found MSVC compiler
    set COMPILER=nmake
)

REM 清理之前的构建文件
echo Cleaning previous build files...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release

REM 运行qmake
echo Running qmake...
qmake esp32_Blockly_Studio.pro
if %errorlevel% neq 0 (
    echo Error: qmake failed
    echo Please check Qt installation and compiler setup
    pause
    exit /b 1
)

REM 编译项目
echo Building project with %COMPILER%...
%COMPILER%
if %errorlevel% neq 0 (
    echo Error: Build failed
    echo Try using Qt Creator for easier compilation
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable should be in the debug or release folder
pause
