#include "codedeploymanager.h"
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QRegularExpression>

CodeDeployManager::CodeDeployManager(QObject *parent)
    : QObject(parent)
    , m_process(nullptr)
    , m_currentState(Idle)
    , m_mpremotePath("mpremote")
    , m_currentProgress(0)
{
    m_process = new QProcess(this);
    
    // 连接进程信号
    connect(m_process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &CodeDeployManager::handleProcessFinished);
    connect(m_process, &QProcess::errorOccurred, this, &CodeDeployManager::handleProcessError);
    connect(m_process, &QProcess::readyReadStandardOutput, this, &CodeDeployManager::handleStandardOutput);
    connect(m_process, &QProcess::readyReadStandardError, this, &CodeDeployManager::handleStandardError);
}

CodeDeployManager::~CodeDeployManager()
{
    if (m_process && m_process->state() != QProcess::NotRunning) {
        m_process->kill();
        m_process->waitForFinished(3000);
    }
    
    cleanupTempFiles();
}

bool CodeDeployManager::deployCode(const QString &code, const QString &portName, const QString &fileName)
{
    if (isDeploying()) {
        qWarning() << "CodeDeployManager: Already deploying";
        return false;
    }

    if (code.isEmpty() || portName.isEmpty()) {
        emit errorOccurred("Code or port name is empty");
        return false;
    }

    // 创建临时代码文件
    m_tempFilePath = createTempCodeFile(code);
    if (m_tempFilePath.isEmpty()) {
        emit errorOccurred("Failed to create temporary code file");
        return false;
    }

    m_currentPortName = portName;
    updateDeployState(Connecting);

    // 构建mpremote命令
    QStringList arguments;
    arguments << "connect" << portName;
    arguments << "cp" << m_tempFilePath << QString(":%1").arg(fileName);
    arguments << "reset";

    qDebug() << "CodeDeployManager: Executing:" << m_mpremotePath << arguments.join(" ");

    m_process->start(m_mpremotePath, arguments);
    
    if (!m_process->waitForStarted(5000)) {
        updateDeployState(Failed);
        emit errorOccurred("Failed to start mpremote process");
        cleanupTempFiles();
        return false;
    }

    return true;
}

void CodeDeployManager::stopDeployment()
{
    if (m_process && m_process->state() != QProcess::NotRunning) {
        m_process->kill();
        updateDeployState(Failed);
        emit deployFinished(false, "Deployment stopped by user");
        cleanupTempFiles();
    }
}

CodeDeployManager::DeployState CodeDeployManager::getCurrentState() const
{
    return m_currentState;
}

bool CodeDeployManager::isDeploying() const
{
    return m_currentState != Idle && m_currentState != Completed && m_currentState != Failed;
}

void CodeDeployManager::setMpremotePath(const QString &path)
{
    m_mpremotePath = path;
}

QString CodeDeployManager::getMpremotePath() const
{
    return m_mpremotePath;
}

bool CodeDeployManager::isMpremoteAvailable() const
{
    QProcess testProcess;
    testProcess.start(m_mpremotePath, QStringList() << "--help");
    testProcess.waitForFinished(5000);
    return testProcess.exitCode() == 0;
}

bool CodeDeployManager::runReplCommand(const QString &command, const QString &portName)
{
    if (isDeploying()) {
        return false;
    }

    QStringList arguments;
    arguments << "connect" << portName;
    arguments << "exec" << command;

    m_process->start(m_mpremotePath, arguments);
    return m_process->waitForStarted(5000);
}

bool CodeDeployManager::listFiles(const QString &portName)
{
    if (isDeploying()) {
        return false;
    }

    QStringList arguments;
    arguments << "connect" << portName;
    arguments << "ls";

    m_process->start(m_mpremotePath, arguments);
    return m_process->waitForStarted(5000);
}

bool CodeDeployManager::deleteFile(const QString &fileName, const QString &portName)
{
    if (isDeploying()) {
        return false;
    }

    QStringList arguments;
    arguments << "connect" << portName;
    arguments << "rm" << fileName;

    m_process->start(m_mpremotePath, arguments);
    return m_process->waitForStarted(5000);
}

void CodeDeployManager::handleProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    Q_UNUSED(exitStatus)

    cleanupTempFiles();

    if (exitCode == 0) {
        updateDeployState(Completed);
        emit deployProgressUpdated(100, "Deployment completed successfully");
        emit deployFinished(true, "Code deployed successfully");
    } else {
        updateDeployState(Failed);
        QString errorOutput = m_process->readAllStandardError();
        emit deployFinished(false, QString("Deployment failed with exit code %1: %2").arg(exitCode).arg(errorOutput));
    }
}

void CodeDeployManager::handleProcessError(QProcess::ProcessError error)
{
    QString errorString;
    switch (error) {
    case QProcess::FailedToStart:
        errorString = "Failed to start mpremote. Please check if mpremote is installed and in PATH.";
        break;
    case QProcess::Crashed:
        errorString = "mpremote process crashed";
        break;
    case QProcess::Timedout:
        errorString = "mpremote process timed out";
        break;
    case QProcess::WriteError:
        errorString = "Write error to mpremote process";
        break;
    case QProcess::ReadError:
        errorString = "Read error from mpremote process";
        break;
    default:
        errorString = "Unknown process error";
        break;
    }

    updateDeployState(Failed);
    emit errorOccurred(errorString);
    emit deployFinished(false, errorString);
    cleanupTempFiles();
}

void CodeDeployManager::handleStandardOutput()
{
    QByteArray data = m_process->readAllStandardOutput();
    QString output = QString::fromUtf8(data);
    
    emit commandOutputReceived(output);
    parseProgress(output);
}

void CodeDeployManager::handleStandardError()
{
    QByteArray data = m_process->readAllStandardError();
    QString output = QString::fromUtf8(data);
    
    emit commandOutputReceived(output);
    
    // 检查是否有错误信息
    if (output.contains("error", Qt::CaseInsensitive) || 
        output.contains("failed", Qt::CaseInsensitive)) {
        emit errorOccurred(output);
    }
}

void CodeDeployManager::updateDeployState(DeployState state)
{
    if (m_currentState != state) {
        m_currentState = state;
        emit deployStateChanged(state);
    }
}

void CodeDeployManager::parseProgress(const QString &output)
{
    // 简单的进度解析逻辑
    if (output.contains("Connecting")) {
        updateDeployState(Connecting);
        emit deployProgressUpdated(10, "Connecting to device...");
    } else if (output.contains("copying") || output.contains("upload")) {
        updateDeployState(Uploading);
        emit deployProgressUpdated(50, "Uploading code...");
    } else if (output.contains("reset") || output.contains("restart")) {
        updateDeployState(Restarting);
        emit deployProgressUpdated(90, "Restarting device...");
    }
}

QString CodeDeployManager::createTempCodeFile(const QString &code)
{
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QString fileName = QString("esp32_blockly_temp_%1.py").arg(QDateTime::currentMSecsSinceEpoch());
    QString filePath = QDir(tempDir).absoluteFilePath(fileName);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "CodeDeployManager: Failed to create temp file:" << filePath;
        return QString();
    }

    QTextStream out(&file);
    out << code;
    file.close();

    return filePath;
}

void CodeDeployManager::cleanupTempFiles()
{
    if (!m_tempFilePath.isEmpty()) {
        QFile::remove(m_tempFilePath);
        m_tempFilePath.clear();
    }
}
