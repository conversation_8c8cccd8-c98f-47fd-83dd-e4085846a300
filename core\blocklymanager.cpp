#include "blocklymanager.h"
#include <QWebEngineView>
#include <QWebChannel>
#include <QJsonDocument>
#include <QJsonObject>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

BlocklyManager::BlocklyManager(QObject *parent)
    : QObject(parent)
    , m_webView(nullptr)
    , m_webChannel(nullptr)
    , m_canUndo(false)
    , m_canRedo(false)
    , m_initialized(false)
{
}

BlocklyManager::~BlocklyManager()
{
    if (m_webChannel) {
        delete m_webChannel;
    }
}

bool BlocklyManager::initialize(QWebEngineView *webView)
{
    if (!webView) {
        qWarning() << "BlocklyManager: WebView is null";
        return false;
    }

    m_webView = webView;
    
    // 设置Web通道
    setupWebChannel();
    
    // 加载Blockly页面
    loadBlocklyPage();
    
    m_initialized = true;
    return true;
}

QString BlocklyManager::getWorkspaceXml() const
{
    if (!m_initialized || !m_webView) {
        return QString();
    }

    // TODO: 通过JavaScript获取工作区XML
    // 这里需要实现与JavaScript的通信
    return QString();
}

void BlocklyManager::setWorkspaceXml(const QString &xml)
{
    if (!m_initialized || !m_webView || xml.isEmpty()) {
        return;
    }

    // TODO: 通过JavaScript设置工作区XML
    // 这里需要实现与JavaScript的通信
}

QString BlocklyManager::getGeneratedCode() const
{
    return m_currentCode;
}

void BlocklyManager::clearWorkspace()
{
    if (!m_initialized || !m_webView) {
        return;
    }

    // TODO: 通过JavaScript清空工作区
}

void BlocklyManager::undo()
{
    if (!m_initialized || !m_webView || !m_canUndo) {
        return;
    }

    // TODO: 通过JavaScript执行撤销操作
}

void BlocklyManager::redo()
{
    if (!m_initialized || !m_webView || !m_canRedo) {
        return;
    }

    // TODO: 通过JavaScript执行重做操作
}

bool BlocklyManager::canUndo() const
{
    return m_canUndo;
}

bool BlocklyManager::canRedo() const
{
    return m_canRedo;
}

void BlocklyManager::handleJavaScriptMessage(const QJsonObject &message)
{
    QString type = message["type"].toString();
    
    if (type == "workspaceChanged") {
        emit workspaceChanged();
    }
    else if (type == "codeChanged") {
        QString code = message["code"].toString();
        m_currentCode = code;
        emit codeChanged(code);
    }
    else if (type == "undoRedoStateChanged") {
        bool canUndo = message["canUndo"].toBool();
        bool canRedo = message["canRedo"].toBool();
        
        if (m_canUndo != canUndo || m_canRedo != canRedo) {
            m_canUndo = canUndo;
            m_canRedo = canRedo;
            emit undoRedoStateChanged(canUndo, canRedo);
        }
    }
}

void BlocklyManager::loadBlocklyPage()
{
    if (!m_webView) {
        return;
    }

    // 加载包含Blockly的HTML页面
    QString htmlPath = "qrc:/resources/blockly/index.html";
    m_webView->load(QUrl(htmlPath));
}

void BlocklyManager::setupWebChannel()
{
    if (!m_webView) {
        return;
    }

    m_webChannel = new QWebChannel(this);
    m_webChannel->registerObject("blocklyManager", this);
    m_webView->page()->setWebChannel(m_webChannel);
}

void BlocklyManager::registerCustomBlocks()
{
    // TODO: 注册ESP32专用的自定义积木块
    // 这里需要定义各种ESP32相关的积木块，如GPIO、WiFi、传感器等
}
