#include "blocklymanager.h"
#include <QDebug>

BlocklyManager::BlocklyManager(QObject *parent)
    : QObject(parent)
    , m_textEdit(nullptr)
    , m_canUndo(false)
    , m_canRedo(false)
    , m_initialized(false)
{
}

BlocklyManager::~BlocklyManager()
{
}

bool BlocklyManager::initialize(QTextEdit *textEdit)
{
    if (!textEdit) {
        qWarning() << "BlocklyManager: TextEdit is null";
        return false;
    }

    m_textEdit = textEdit;
    m_initialized = true;

    // 生成示例代码
    generateSampleCode();
    updateCodeDisplay();

    return true;
}

QString BlocklyManager::getWorkspaceXml() const
{
    return m_workspaceXml;
}

void BlocklyManager::setWorkspaceXml(const QString &xml)
{
    if (m_workspaceXml != xml) {
        m_workspaceXml = xml;

        // 根据XML生成对应的代码（简化版本）
        generateSampleCode();
        updateCodeDisplay();

        emit workspaceChanged();
    }
}

QString BlocklyManager::getGeneratedCode() const
{
    return m_currentCode;
}

void BlocklyManager::clearWorkspace()
{
    m_workspaceXml.clear();
    m_currentCode.clear();

    if (m_textEdit) {
        m_textEdit->clear();
        m_textEdit->setPlainText("# Workspace cleared\n# Start building your ESP32 program!");
    }

    emit workspaceChanged();
    emit codeChanged(m_currentCode);
}

void BlocklyManager::undo()
{
    // 简化版本的撤销功能
    if (m_canUndo) {
        m_canUndo = false;
        m_canRedo = true;
        emit undoRedoStateChanged(m_canUndo, m_canRedo);
    }
}

void BlocklyManager::redo()
{
    // 简化版本的重做功能
    if (m_canRedo) {
        m_canRedo = false;
        m_canUndo = true;
        emit undoRedoStateChanged(m_canUndo, m_canRedo);
    }
}

bool BlocklyManager::canUndo() const
{
    return m_canUndo;
}

bool BlocklyManager::canRedo() const
{
    return m_canRedo;
}

void BlocklyManager::generateSampleCode()
{
    // 生成示例ESP32 MicroPython代码
    m_currentCode = R"(# ESP32 MicroPython Program
# Generated by ESP32 Blockly Studio

from machine import Pin, PWM
import time

# Initialize LED pin
led = Pin(2, Pin.OUT)

# Main program loop
def main():
    print("ESP32 Blockly Studio - Sample Program")
    print("Blinking LED on GPIO 2")

    while True:
        led.on()
        print("LED ON")
        time.sleep(1)

        led.off()
        print("LED OFF")
        time.sleep(1)

# Run the main program
if __name__ == "__main__":
    main()
)";

    emit codeChanged(m_currentCode);
}

void BlocklyManager::updateCodeDisplay()
{
    if (m_textEdit && m_initialized) {
        m_textEdit->setPlainText(m_currentCode);
    }
}