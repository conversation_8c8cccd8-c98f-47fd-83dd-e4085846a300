#include "terminalwidget.h"
#include <QDateTime>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <QScrollBar>
#include <QKeyEvent>
#include <QApplication>

TerminalWidget::TerminalWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_toolBarLayout(nullptr)
    , m_terminalDisplay(nullptr)
    , m_inputLine(nullptr)
    , m_clearButton(nullptr)
    , m_saveButton(nullptr)
    , m_modeComboBox(nullptr)
    , m_autoScrollCheckBox(nullptr)
    , m_timestampCheckBox(nullptr)
    , m_maxLinesSpinBox(nullptr)
    , m_promptLabel(nullptr)
    , m_currentMode(Monitor)
    , m_autoScroll(true)
    , m_showTimestamp(false)
    , m_maxLines(1000)
    , m_prompt(">>> ")
    , m_currentLineCount(0)
{
    initializeUI();
    updateUIState();
}

TerminalWidget::~TerminalWidget()
{
}

void TerminalWidget::setTerminalMode(TerminalMode mode)
{
    if (m_currentMode != mode) {
        m_currentMode = mode;
        m_modeComboBox->setCurrentIndex(static_cast<int>(mode));
        updateUIState();
        emit terminalModeChanged(mode);
    }
}

TerminalWidget::TerminalMode TerminalWidget::getTerminalMode() const
{
    return m_currentMode;
}

void TerminalWidget::appendText(const QString &text)
{
    appendColoredText(text, QColor(Qt::black));
}

void TerminalWidget::appendColoredText(const QString &text, const QColor &color)
{
    if (text.isEmpty()) {
        return;
    }

    QString formattedText = text;
    
    // 添加时间戳
    if (m_showTimestamp) {
        QString timestamp = getCurrentTimestamp();
        formattedText = QString("[%1] %2").arg(timestamp, text);
    }

    // 设置文本颜色
    m_terminalDisplay->setTextColor(color);
    m_terminalDisplay->append(formattedText);
    
    m_currentLineCount++;
    
    // 限制行数
    limitTerminalLines();
    
    // 自动滚动到底部
    if (m_autoScroll) {
        scrollToBottom();
    }
}

void TerminalWidget::clearTerminal()
{
    m_terminalDisplay->clear();
    m_currentLineCount = 0;
    emit clearRequested();
}

void TerminalWidget::setAutoScroll(bool autoScroll)
{
    if (m_autoScroll != autoScroll) {
        m_autoScroll = autoScroll;
        m_autoScrollCheckBox->setChecked(autoScroll);
    }
}

bool TerminalWidget::getAutoScroll() const
{
    return m_autoScroll;
}

void TerminalWidget::setShowTimestamp(bool showTimestamp)
{
    if (m_showTimestamp != showTimestamp) {
        m_showTimestamp = showTimestamp;
        m_timestampCheckBox->setChecked(showTimestamp);
    }
}

bool TerminalWidget::getShowTimestamp() const
{
    return m_showTimestamp;
}

void TerminalWidget::setMaxLines(int maxLines)
{
    if (m_maxLines != maxLines) {
        m_maxLines = maxLines;
        m_maxLinesSpinBox->setValue(maxLines);
        limitTerminalLines();
    }
}

int TerminalWidget::getMaxLines() const
{
    return m_maxLines;
}

void TerminalWidget::setTerminalFont(const QFont &font)
{
    m_terminalDisplay->setFont(font);
    m_inputLine->setFont(font);
}

QFont TerminalWidget::getTerminalFont() const
{
    return m_terminalDisplay->font();
}

bool TerminalWidget::saveToFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "Save Error", 
                           QString("Failed to save file: %1").arg(file.errorString()));
        return false;
    }

    QTextStream out(&file);
    out << m_terminalDisplay->toPlainText();
    file.close();

    return true;
}

QString TerminalWidget::getTerminalContent() const
{
    return m_terminalDisplay->toPlainText();
}

void TerminalWidget::setPrompt(const QString &prompt)
{
    if (m_prompt != prompt) {
        m_prompt = prompt;
        m_promptLabel->setText(prompt);
    }
}

QString TerminalWidget::getPrompt() const
{
    return m_prompt;
}

void TerminalWidget::handleInputReturn()
{
    QString command = m_inputLine->text();
    m_inputLine->clear();
    
    if (!command.isEmpty()) {
        // 在终端显示输入的命令
        appendColoredText(m_prompt + command, QColor(Qt::blue));
        emit commandEntered(command);
    }
}

void TerminalWidget::handleClearClicked()
{
    clearTerminal();
}

void TerminalWidget::handleSaveClicked()
{
    QString fileName = QFileDialog::getSaveFileName(this, 
                                                   "Save Terminal Output", 
                                                   "terminal_output.txt",
                                                   "Text Files (*.txt);;All Files (*)");
    if (!fileName.isEmpty()) {
        if (saveToFile(fileName)) {
            emit saveRequested();
        }
    }
}

void TerminalWidget::handleAutoScrollChanged(bool checked)
{
    setAutoScroll(checked);
}

void TerminalWidget::handleTimestampChanged(bool checked)
{
    setShowTimestamp(checked);
}

void TerminalWidget::handleMaxLinesChanged(int maxLines)
{
    setMaxLines(maxLines);
}

void TerminalWidget::handleModeChanged(int index)
{
    setTerminalMode(static_cast<TerminalMode>(index));
}

void TerminalWidget::initializeUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);

    // 创建工具栏
    createToolBar();

    // 创建终端显示区域
    m_terminalDisplay = new QTextEdit(this);
    m_terminalDisplay->setReadOnly(true);
    m_terminalDisplay->setFont(QFont("Consolas", 10));
    m_terminalDisplay->setStyleSheet("background-color: #1e1e1e; color: #ffffff;");
    m_mainLayout->addWidget(m_terminalDisplay);

    // 创建输入区域
    QHBoxLayout *inputLayout = new QHBoxLayout();
    
    m_promptLabel = new QLabel(m_prompt, this);
    m_promptLabel->setFont(QFont("Consolas", 10));
    m_promptLabel->setStyleSheet("color: #00ff00;");
    inputLayout->addWidget(m_promptLabel);
    
    m_inputLine = new QLineEdit(this);
    m_inputLine->setFont(QFont("Consolas", 10));
    connect(m_inputLine, &QLineEdit::returnPressed, this, &TerminalWidget::handleInputReturn);
    inputLayout->addWidget(m_inputLine);
    
    m_mainLayout->addLayout(inputLayout);

    setLayout(m_mainLayout);
}

void TerminalWidget::createToolBar()
{
    m_toolBarLayout = new QHBoxLayout();
    
    // 模式选择
    m_toolBarLayout->addWidget(new QLabel("Mode:", this));
    m_modeComboBox = new QComboBox(this);
    m_modeComboBox->addItem("Monitor");
    m_modeComboBox->addItem("REPL");
    connect(m_modeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TerminalWidget::handleModeChanged);
    m_toolBarLayout->addWidget(m_modeComboBox);
    
    m_toolBarLayout->addSpacing(10);
    
    // 自动滚动
    m_autoScrollCheckBox = new QCheckBox("Auto Scroll", this);
    m_autoScrollCheckBox->setChecked(m_autoScroll);
    connect(m_autoScrollCheckBox, &QCheckBox::toggled, this, &TerminalWidget::handleAutoScrollChanged);
    m_toolBarLayout->addWidget(m_autoScrollCheckBox);
    
    // 时间戳
    m_timestampCheckBox = new QCheckBox("Timestamp", this);
    m_timestampCheckBox->setChecked(m_showTimestamp);
    connect(m_timestampCheckBox, &QCheckBox::toggled, this, &TerminalWidget::handleTimestampChanged);
    m_toolBarLayout->addWidget(m_timestampCheckBox);
    
    m_toolBarLayout->addSpacing(10);
    
    // 最大行数
    m_toolBarLayout->addWidget(new QLabel("Max Lines:", this));
    m_maxLinesSpinBox = new QSpinBox(this);
    m_maxLinesSpinBox->setRange(0, 10000);
    m_maxLinesSpinBox->setValue(m_maxLines);
    m_maxLinesSpinBox->setSpecialValueText("Unlimited");
    connect(m_maxLinesSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TerminalWidget::handleMaxLinesChanged);
    m_toolBarLayout->addWidget(m_maxLinesSpinBox);
    
    m_toolBarLayout->addStretch();
    
    // 清空按钮
    m_clearButton = new QPushButton("Clear", this);
    connect(m_clearButton, &QPushButton::clicked, this, &TerminalWidget::handleClearClicked);
    m_toolBarLayout->addWidget(m_clearButton);
    
    // 保存按钮
    m_saveButton = new QPushButton("Save", this);
    connect(m_saveButton, &QPushButton::clicked, this, &TerminalWidget::handleSaveClicked);
    m_toolBarLayout->addWidget(m_saveButton);
    
    m_mainLayout->addLayout(m_toolBarLayout);
}

void TerminalWidget::updateUIState()
{
    // 根据模式更新UI状态
    bool isReplMode = (m_currentMode == REPL);
    m_inputLine->setEnabled(isReplMode);
    m_promptLabel->setVisible(isReplMode);
    
    if (isReplMode) {
        m_inputLine->setFocus();
    }
}

void TerminalWidget::limitTerminalLines()
{
    if (m_maxLines <= 0) {
        return; // 无限制
    }
    
    if (m_currentLineCount > m_maxLines) {
        // 删除多余的行
        QTextCursor cursor = m_terminalDisplay->textCursor();
        cursor.movePosition(QTextCursor::Start);
        
        int linesToRemove = m_currentLineCount - m_maxLines;
        for (int i = 0; i < linesToRemove; ++i) {
            cursor.select(QTextCursor::LineUnderCursor);
            cursor.removeSelectedText();
            cursor.deleteChar(); // 删除换行符
        }
        
        m_currentLineCount = m_maxLines;
    }
}

QString TerminalWidget::getCurrentTimestamp() const
{
    return QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
}

void TerminalWidget::scrollToBottom()
{
    QScrollBar *scrollBar = m_terminalDisplay->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}
