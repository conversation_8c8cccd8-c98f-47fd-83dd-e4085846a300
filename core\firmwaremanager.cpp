#include "firmwaremanager.h"
#include <QNetworkRequest>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QDebug>
#include <QRegularExpression>

FirmwareManager::FirmwareManager(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_currentReply(nullptr)
    , m_flashProcess(new QProcess(this))
    , m_currentState(Idle)
    , m_esptoolPath("esptool.py")
    , m_updateCheckUrl("https://micropython.org/resources/firmware/esp32-20240222-v1.22.2.bin")
    , m_flashProgress(0)
{
    // 连接网络管理器信号
    connect(m_networkManager, &QNetworkAccessManager::finished, 
            this, &FirmwareManager::handleUpdateCheckReply);

    // 连接烧录进程信号
    connect(m_flashProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &FirmwareManager::handleFlashProcessFinished);
    connect(m_flashProcess, &QProcess::errorOccurred, 
            this, &FirmwareManager::handleFlashProcessError);
    connect(m_flashProcess, &QProcess::readyReadStandardOutput, 
            this, &FirmwareManager::handleFlashProcessOutput);
    connect(m_flashProcess, &QProcess::readyReadStandardError, 
            this, &FirmwareManager::handleFlashProcessOutput);
}

FirmwareManager::~FirmwareManager()
{
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
    }
    
    if (m_flashProcess && m_flashProcess->state() != QProcess::NotRunning) {
        m_flashProcess->kill();
        m_flashProcess->waitForFinished(3000);
    }
}

void FirmwareManager::checkForUpdates(const QString &currentVersion)
{
    if (isBusy()) {
        qWarning() << "FirmwareManager: Already busy";
        return;
    }

    updateOperationState(CheckingUpdate);

    QNetworkRequest request(QUrl(m_updateCheckUrl));
    request.setHeader(QNetworkRequest::UserAgentHeader, "ESP32-Blockly-Studio/1.0");
    
    m_currentReply = m_networkManager->get(request);
    
    // 连接下载信号（用于检查更新时的进度）
    connect(m_currentReply, &QNetworkReply::finished, 
            this, &FirmwareManager::handleUpdateCheckReply);
}

bool FirmwareManager::downloadFirmware(const FirmwareInfo &firmwareInfo, const QString &downloadPath)
{
    if (isBusy()) {
        qWarning() << "FirmwareManager: Already busy";
        return false;
    }

    if (firmwareInfo.downloadUrl.isEmpty()) {
        emit errorOccurred("Download URL is empty");
        return false;
    }

    updateOperationState(Downloading);

    // 确定下载路径
    if (downloadPath.isEmpty()) {
        m_downloadFilePath = getDefaultDownloadPath();
    } else {
        m_downloadFilePath = downloadPath;
    }

    // 确保下载目录存在
    QDir().mkpath(QFileInfo(m_downloadFilePath).absolutePath());

    QNetworkRequest request(QUrl(firmwareInfo.downloadUrl));
    request.setHeader(QNetworkRequest::UserAgentHeader, "ESP32-Blockly-Studio/1.0");
    
    m_currentReply = m_networkManager->get(request);
    
    // 连接下载信号
    connect(m_currentReply, &QNetworkReply::downloadProgress, 
            this, &FirmwareManager::handleDownloadProgress);
    connect(m_currentReply, &QNetworkReply::finished, 
            this, &FirmwareManager::handleDownloadReply);

    return true;
}

bool FirmwareManager::flashFirmware(const QString &firmwarePath, const QString &portName, const QString &chipType)
{
    if (isBusy()) {
        qWarning() << "FirmwareManager: Already busy";
        return false;
    }

    if (firmwarePath.isEmpty() || portName.isEmpty()) {
        emit errorOccurred("Firmware path or port name is empty");
        return false;
    }

    if (!QFile::exists(firmwarePath)) {
        emit errorOccurred("Firmware file does not exist: " + firmwarePath);
        return false;
    }

    updateOperationState(Flashing);
    m_flashProgress = 0;

    // 构建esptool命令
    QStringList arguments;
    arguments << "--chip" << chipType;
    arguments << "--port" << portName;
    arguments << "--baud" << "460800";
    arguments << "write_flash";
    arguments << "-z";
    arguments << "0x1000" << firmwarePath;

    qDebug() << "FirmwareManager: Executing:" << m_esptoolPath << arguments.join(" ");

    m_flashProcess->start(m_esptoolPath, arguments);
    
    if (!m_flashProcess->waitForStarted(5000)) {
        updateOperationState(Failed);
        emit errorOccurred("Failed to start esptool process");
        return false;
    }

    return true;
}

void FirmwareManager::stopCurrentOperation()
{
    if (m_currentReply) {
        m_currentReply->abort();
    }
    
    if (m_flashProcess && m_flashProcess->state() != QProcess::NotRunning) {
        m_flashProcess->kill();
    }
    
    updateOperationState(Failed);
    emit errorOccurred("Operation stopped by user");
}

FirmwareManager::OperationState FirmwareManager::getCurrentState() const
{
    return m_currentState;
}

bool FirmwareManager::isBusy() const
{
    return m_currentState != Idle && m_currentState != Completed && m_currentState != Failed;
}

void FirmwareManager::setEsptoolPath(const QString &path)
{
    m_esptoolPath = path;
}

QString FirmwareManager::getEsptoolPath() const
{
    return m_esptoolPath;
}

bool FirmwareManager::isEsptoolAvailable() const
{
    QProcess testProcess;
    testProcess.start(m_esptoolPath, QStringList() << "version");
    testProcess.waitForFinished(5000);
    return testProcess.exitCode() == 0;
}

void FirmwareManager::setUpdateCheckUrl(const QString &url)
{
    m_updateCheckUrl = url;
}

QString FirmwareManager::getUpdateCheckUrl() const
{
    return m_updateCheckUrl;
}

FirmwareManager::FirmwareInfo FirmwareManager::getLatestFirmwareInfo() const
{
    return m_latestFirmwareInfo;
}

bool FirmwareManager::eraseFlash(const QString &portName)
{
    if (isBusy()) {
        return false;
    }

    updateOperationState(Flashing);

    QStringList arguments;
    arguments << "--port" << portName;
    arguments << "erase_flash";

    m_flashProcess->start(m_esptoolPath, arguments);
    return m_flashProcess->waitForStarted(5000);
}

void FirmwareManager::handleUpdateCheckReply()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }

    reply->deleteLater();
    m_currentReply = nullptr;

    if (reply->error() != QNetworkReply::NoError) {
        updateOperationState(Failed);
        emit errorOccurred("Failed to check for updates: " + reply->errorString());
        return;
    }

    // 这里简化处理，实际应该解析JSON响应
    // 假设我们有最新的固件信息
    m_latestFirmwareInfo.version = "v1.22.2";
    m_latestFirmwareInfo.downloadUrl = m_updateCheckUrl;
    m_latestFirmwareInfo.description = "Latest MicroPython firmware for ESP32";
    m_latestFirmwareInfo.releaseDate = "2024-02-22";
    m_latestFirmwareInfo.fileSize = 1024 * 1024; // 1MB
    
    updateOperationState(Completed);
    emit updateCheckFinished(true, m_latestFirmwareInfo);
}

void FirmwareManager::handleDownloadReply()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }

    reply->deleteLater();
    m_currentReply = nullptr;

    if (reply->error() != QNetworkReply::NoError) {
        updateOperationState(Failed);
        emit downloadFinished(false, QString());
        emit errorOccurred("Download failed: " + reply->errorString());
        return;
    }

    // 保存下载的文件
    QFile file(m_downloadFilePath);
    if (!file.open(QIODevice::WriteOnly)) {
        updateOperationState(Failed);
        emit downloadFinished(false, QString());
        emit errorOccurred("Failed to save downloaded file: " + file.errorString());
        return;
    }

    file.write(reply->readAll());
    file.close();

    updateOperationState(Completed);
    emit downloadFinished(true, m_downloadFilePath);
}

void FirmwareManager::handleDownloadProgress(qint64 bytesReceived, qint64 bytesTotal)
{
    emit downloadProgressUpdated(bytesReceived, bytesTotal);
}

void FirmwareManager::handleFlashProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    Q_UNUSED(exitStatus)

    if (exitCode == 0) {
        updateOperationState(Completed);
        emit flashProgressUpdated(100, "Flashing completed successfully");
        emit flashFinished(true, "Firmware flashed successfully");
    } else {
        updateOperationState(Failed);
        QString errorOutput = m_flashProcess->readAllStandardError();
        emit flashFinished(false, QString("Flashing failed with exit code %1: %2").arg(exitCode).arg(errorOutput));
    }
}

void FirmwareManager::handleFlashProcessError(QProcess::ProcessError error)
{
    QString errorString;
    switch (error) {
    case QProcess::FailedToStart:
        errorString = "Failed to start esptool. Please check if esptool is installed and in PATH.";
        break;
    case QProcess::Crashed:
        errorString = "esptool process crashed";
        break;
    case QProcess::Timedout:
        errorString = "esptool process timed out";
        break;
    default:
        errorString = "Unknown process error";
        break;
    }

    updateOperationState(Failed);
    emit errorOccurred(errorString);
    emit flashFinished(false, errorString);
}

void FirmwareManager::handleFlashProcessOutput()
{
    QByteArray data = m_flashProcess->readAllStandardOutput();
    QString output = QString::fromUtf8(data);
    
    parseFlashProgress(output);
}

void FirmwareManager::updateOperationState(OperationState state)
{
    if (m_currentState != state) {
        m_currentState = state;
        emit operationStateChanged(state);
    }
}

FirmwareManager::FirmwareInfo FirmwareManager::parseFirmwareInfo(const QJsonObject &json)
{
    FirmwareInfo info;
    info.version = json["version"].toString();
    info.downloadUrl = json["download_url"].toString();
    info.description = json["description"].toString();
    info.releaseDate = json["release_date"].toString();
    info.fileSize = json["file_size"].toInt();
    info.checksum = json["checksum"].toString();
    return info;
}

void FirmwareManager::parseFlashProgress(const QString &output)
{
    // 简单的进度解析
    QRegularExpression progressRegex(R"((\d+)%)");
    QRegularExpressionMatch match = progressRegex.match(output);
    
    if (match.hasMatch()) {
        int progress = match.captured(1).toInt();
        if (progress != m_flashProgress) {
            m_flashProgress = progress;
            emit flashProgressUpdated(progress, QString("Flashing... %1%").arg(progress));
        }
    }
}

QString FirmwareManager::getDefaultDownloadPath() const
{
    QString downloadsPath = QStandardPaths::writableLocation(QStandardPaths::DownloadLocation);
    return QDir(downloadsPath).absoluteFilePath("esp32_firmware.bin");
}
