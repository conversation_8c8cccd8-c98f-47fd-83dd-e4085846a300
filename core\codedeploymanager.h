#ifndef CODEDEPLOYMANAGER_H
#define CODEDEPLOYMANAGER_H

#include <QObject>
#include <QProcess>
#include <QString>

/**
 * @brief 代码部署管理器
 * 
 * 负责将生成的MicroPython代码部署到ESP32设备
 * 实现PRD中FR-2.3的需求：
 * - FR-2.3.2: 一键运行（通过mpremote工具）
 * - FR-2.3.3: 部署状态反馈
 */
class CodeDeployManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 部署状态枚举
     */
    enum DeployState {
        Idle,           ///< 空闲状态
        Connecting,     ///< 连接中
        Uploading,      ///< 上传代码中
        Restarting,     ///< 重启设备中
        Completed,      ///< 部署完成
        Failed          ///< 部署失败
    };

    explicit CodeDeployManager(QObject *parent = nullptr);
    ~CodeDeployManager();

    /**
     * @brief 部署代码到ESP32
     * @param code MicroPython代码
     * @param portName 串口名称
     * @param fileName 目标文件名，默认为"main.py"
     * @return 部署是否开始成功
     */
    bool deployCode(const QString &code, const QString &portName, const QString &fileName = "main.py");

    /**
     * @brief 停止当前部署操作
     */
    void stopDeployment();

    /**
     * @brief 获取当前部署状态
     * @return 部署状态
     */
    DeployState getCurrentState() const;

    /**
     * @brief 检查是否正在部署
     * @return 是否正在部署
     */
    bool isDeploying() const;

    /**
     * @brief 设置mpremote工具路径
     * @param path mpremote可执行文件路径
     */
    void setMpremotePath(const QString &path);

    /**
     * @brief 获取mpremote工具路径
     * @return mpremote可执行文件路径
     */
    QString getMpremotePath() const;

    /**
     * @brief 检查mpremote工具是否可用
     * @return 工具是否可用
     */
    bool isMpremoteAvailable() const;

    /**
     * @brief 运行REPL命令
     * @param command 要执行的命令
     * @param portName 串口名称
     * @return 命令是否开始执行成功
     */
    bool runReplCommand(const QString &command, const QString &portName);

    /**
     * @brief 列出设备上的文件
     * @param portName 串口名称
     * @return 命令是否开始执行成功
     */
    bool listFiles(const QString &portName);

    /**
     * @brief 删除设备上的文件
     * @param fileName 文件名
     * @param portName 串口名称
     * @return 命令是否开始执行成功
     */
    bool deleteFile(const QString &fileName, const QString &portName);

signals:
    /**
     * @brief 部署状态发生变化时发出
     * @param state 新的部署状态
     */
    void deployStateChanged(DeployState state);

    /**
     * @brief 部署进度更新时发出
     * @param progress 进度百分比 (0-100)
     * @param message 进度消息
     */
    void deployProgressUpdated(int progress, const QString &message);

    /**
     * @brief 部署完成时发出
     * @param success 是否成功
     * @param message 完成消息
     */
    void deployFinished(bool success, const QString &message);

    /**
     * @brief 接收到命令输出时发出
     * @param output 输出内容
     */
    void commandOutputReceived(const QString &output);

    /**
     * @brief 发生错误时发出
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private slots:
    /**
     * @brief 处理进程完成
     * @param exitCode 退出代码
     * @param exitStatus 退出状态
     */
    void handleProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);

    /**
     * @brief 处理进程错误
     * @param error 进程错误类型
     */
    void handleProcessError(QProcess::ProcessError error);

    /**
     * @brief 处理标准输出
     */
    void handleStandardOutput();

    /**
     * @brief 处理标准错误输出
     */
    void handleStandardError();

private:
    /**
     * @brief 更新部署状态
     * @param state 新状态
     */
    void updateDeployState(DeployState state);

    /**
     * @brief 解析mpremote输出以获取进度信息
     * @param output 输出内容
     */
    void parseProgress(const QString &output);

    /**
     * @brief 创建临时代码文件
     * @param code 代码内容
     * @return 临时文件路径
     */
    QString createTempCodeFile(const QString &code);

    /**
     * @brief 清理临时文件
     */
    void cleanupTempFiles();

private:
    QProcess *m_process;
    DeployState m_currentState;
    QString m_mpremotePath;
    QString m_currentPortName;
    QString m_tempFilePath;
    int m_currentProgress;
};

#endif // CODEDEPLOYMANAGER_H
