#ifndef SERIALMANAGER_H
#define SERIALMANAGER_H

#include <QObject>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QStringList>
#include <QTimer>

/**
 * @brief 串口通信管理器
 * 
 * 负责ESP32设备的检测、连接和数据传输
 * 实现PRD中FR-2.3.1和FR-4的相关需求：
 * - 自动检测可用COM端口
 * - 建立与ESP32的串口连接
 * - 数据收发功能
 * - 串口监视器功能
 */
class SerialManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 连接状态枚举
     */
    enum ConnectionState {
        Disconnected,   ///< 未连接
        Connecting,     ///< 连接中
        Connected,      ///< 已连接
        Error          ///< 连接错误
    };

    explicit SerialManager(QObject *parent = nullptr);
    ~SerialManager();

    /**
     * @brief 获取可用的串口列表
     * @return 串口名称列表
     */
    QStringList getAvailablePorts() const;

    /**
     * @brief 连接到指定串口
     * @param portName 串口名称
     * @param baudRate 波特率，默认115200
     * @return 连接是否成功
     */
    bool connectToPort(const QString &portName, int baudRate = 115200);

    /**
     * @brief 断开串口连接
     */
    void disconnect();

    /**
     * @brief 发送数据到串口
     * @param data 要发送的数据
     * @return 发送是否成功
     */
    bool sendData(const QByteArray &data);

    /**
     * @brief 发送文本到串口
     * @param text 要发送的文本
     * @return 发送是否成功
     */
    bool sendText(const QString &text);

    /**
     * @brief 获取当前连接状态
     * @return 连接状态
     */
    ConnectionState getConnectionState() const;

    /**
     * @brief 获取当前连接的串口名称
     * @return 串口名称，未连接时返回空字符串
     */
    QString getCurrentPortName() const;

    /**
     * @brief 获取当前波特率
     * @return 波特率
     */
    int getCurrentBaudRate() const;

    /**
     * @brief 检查是否已连接
     * @return 是否已连接
     */
    bool isConnected() const;

    /**
     * @brief 启动端口自动检测
     * @param interval 检测间隔（毫秒），默认1000ms
     */
    void startPortDetection(int interval = 1000);

    /**
     * @brief 停止端口自动检测
     */
    void stopPortDetection();

signals:
    /**
     * @brief 连接状态发生变化时发出
     * @param state 新的连接状态
     */
    void connectionStateChanged(ConnectionState state);

    /**
     * @brief 接收到数据时发出
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray &data);

    /**
     * @brief 接收到文本时发出
     * @param text 接收到的文本
     */
    void textReceived(const QString &text);

    /**
     * @brief 可用端口列表发生变化时发出
     * @param ports 新的端口列表
     */
    void availablePortsChanged(const QStringList &ports);

    /**
     * @brief 发生错误时发出
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private slots:
    /**
     * @brief 处理串口数据接收
     */
    void handleDataReceived();

    /**
     * @brief 处理串口错误
     * @param error 串口错误类型
     */
    void handleSerialError(QSerialPort::SerialPortError error);

    /**
     * @brief 检测可用端口变化
     */
    void detectPortChanges();

private:
    /**
     * @brief 更新连接状态
     * @param state 新状态
     */
    void updateConnectionState(ConnectionState state);

private:
    QSerialPort *m_serialPort;
    ConnectionState m_connectionState;
    QString m_currentPortName;
    int m_currentBaudRate;
    QStringList m_lastAvailablePorts;
    QTimer *m_portDetectionTimer;
    QByteArray m_receiveBuffer;
};

#endif // SERIALMANAGER_H
