#ifndef PROJECTMANAGER_H
#define PROJECTMANAGER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>

/**
 * @brief 项目文件管理器
 * 
 * 负责项目的新建、保存、打开和导出功能
 * 实现PRD中FR-2的相关需求：
 * - FR-2.1: 项目文件管理
 * - FR-2.2: 代码生成与导出
 */
class ProjectManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 项目信息结构
     */
    struct ProjectInfo {
        QString name;           ///< 项目名称
        QString description;    ///< 项目描述
        QString author;         ///< 作者
        QString version;        ///< 版本号
        QString createDate;     ///< 创建日期
        QString modifyDate;     ///< 修改日期
        QString filePath;       ///< 文件路径
    };

    /**
     * @brief 项目数据结构
     */
    struct ProjectData {
        ProjectInfo info;       ///< 项目信息
        QString workspaceXml;   ///< Blockly工作区XML
        QString generatedCode;  ///< 生成的代码
        QJsonObject settings;   ///< 项目设置
    };

    explicit ProjectManager(QObject *parent = nullptr);
    ~ProjectManager();

    /**
     * @brief 创建新项目
     * @param name 项目名称
     * @param description 项目描述
     * @param author 作者
     * @return 创建是否成功
     */
    bool createNewProject(const QString &name, const QString &description = QString(), const QString &author = QString());

    /**
     * @brief 保存项目
     * @param filePath 保存路径，空字符串使用当前路径
     * @return 保存是否成功
     */
    bool saveProject(const QString &filePath = QString());

    /**
     * @brief 另存为项目
     * @param filePath 保存路径
     * @return 保存是否成功
     */
    bool saveProjectAs(const QString &filePath);

    /**
     * @brief 打开项目
     * @param filePath 项目文件路径
     * @return 打开是否成功
     */
    bool openProject(const QString &filePath);

    /**
     * @brief 关闭当前项目
     * @return 关闭是否成功
     */
    bool closeProject();

    /**
     * @brief 导出代码到文件
     * @param filePath 导出路径
     * @param code 要导出的代码
     * @return 导出是否成功
     */
    bool exportCode(const QString &filePath, const QString &code);

    /**
     * @brief 获取当前项目信息
     * @return 项目信息
     */
    ProjectInfo getCurrentProjectInfo() const;

    /**
     * @brief 获取当前项目数据
     * @return 项目数据
     */
    ProjectData getCurrentProjectData() const;

    /**
     * @brief 设置工作区XML
     * @param xml 工作区XML
     */
    void setWorkspaceXml(const QString &xml);

    /**
     * @brief 获取工作区XML
     * @return 工作区XML
     */
    QString getWorkspaceXml() const;

    /**
     * @brief 设置生成的代码
     * @param code 生成的代码
     */
    void setGeneratedCode(const QString &code);

    /**
     * @brief 获取生成的代码
     * @return 生成的代码
     */
    QString getGeneratedCode() const;

    /**
     * @brief 设置项目设置
     * @param settings 设置JSON对象
     */
    void setProjectSettings(const QJsonObject &settings);

    /**
     * @brief 获取项目设置
     * @return 设置JSON对象
     */
    QJsonObject getProjectSettings() const;

    /**
     * @brief 检查项目是否已修改
     * @return 是否已修改
     */
    bool isProjectModified() const;

    /**
     * @brief 检查是否有打开的项目
     * @return 是否有打开的项目
     */
    bool hasOpenProject() const;

    /**
     * @brief 获取当前项目文件路径
     * @return 项目文件路径
     */
    QString getCurrentProjectPath() const;

    /**
     * @brief 获取项目文件扩展名
     * @return 文件扩展名
     */
    static QString getProjectFileExtension();

    /**
     * @brief 获取项目文件过滤器（用于文件对话框）
     * @return 文件过滤器字符串
     */
    static QString getProjectFileFilter();

    /**
     * @brief 验证项目文件
     * @param filePath 项目文件路径
     * @return 文件是否有效
     */
    static bool validateProjectFile(const QString &filePath);

signals:
    /**
     * @brief 项目创建时发出
     * @param projectInfo 项目信息
     */
    void projectCreated(const ProjectInfo &projectInfo);

    /**
     * @brief 项目打开时发出
     * @param projectData 项目数据
     */
    void projectOpened(const ProjectData &projectData);

    /**
     * @brief 项目保存时发出
     * @param filePath 保存路径
     */
    void projectSaved(const QString &filePath);

    /**
     * @brief 项目关闭时发出
     */
    void projectClosed();

    /**
     * @brief 项目修改状态发生变化时发出
     * @param modified 是否已修改
     */
    void projectModifiedChanged(bool modified);

    /**
     * @brief 代码导出时发出
     * @param filePath 导出路径
     */
    void codeExported(const QString &filePath);

    /**
     * @brief 发生错误时发出
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private:
    /**
     * @brief 标记项目为已修改
     */
    void markProjectModified();

    /**
     * @brief 清除修改标记
     */
    void clearModifiedFlag();

    /**
     * @brief 生成项目JSON
     * @return JSON文档
     */
    QJsonDocument generateProjectJson() const;

    /**
     * @brief 解析项目JSON
     * @param json JSON文档
     * @return 解析是否成功
     */
    bool parseProjectJson(const QJsonDocument &json);

    /**
     * @brief 初始化默认项目数据
     */
    void initializeDefaultProject();

    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    QString getCurrentTimeString() const;

private:
    ProjectData m_currentProject;
    bool m_isModified;
    bool m_hasOpenProject;
    QString m_currentFilePath;
};

#endif // PROJECTMANAGER_H
