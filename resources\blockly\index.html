<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ESP32 Blockly Studio</title>
    <script src="https://unpkg.com/blockly/blockly.min.js"></script>
    <script src="https://unpkg.com/blockly/python.js"></script>
    <script src="https://unpkg.com/blockly/msg/en.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #blocklyDiv {
            height: 100vh;
            width: 100%;
        }
        
        .toolbox {
            background-color: #f0f0f0;
        }
        
        .category {
            color: #333;
        }
    </style>
</head>
<body>
    <div id="blocklyDiv"></div>

    <!-- Blockly工具箱定义 -->
    <xml xmlns="https://developers.google.com/blockly/xml" id="toolbox" style="display: none">
        <!-- 基础逻辑 -->
        <category name="Logic" colour="#5C81A6">
            <block type="controls_if"></block>
            <block type="logic_compare"></block>
            <block type="logic_operation"></block>
            <block type="logic_negate"></block>
            <block type="logic_boolean"></block>
            <block type="logic_null"></block>
            <block type="logic_ternary"></block>
        </category>
        
        <!-- 循环 -->
        <category name="Loops" colour="#5CA65C">
            <block type="controls_repeat_ext"></block>
            <block type="controls_whileUntil"></block>
            <block type="controls_for"></block>
            <block type="controls_forEach"></block>
            <block type="controls_flow_statements"></block>
        </category>
        
        <!-- 数学 -->
        <category name="Math" colour="#5C68A6">
            <block type="math_number"></block>
            <block type="math_arithmetic"></block>
            <block type="math_single"></block>
            <block type="math_trig"></block>
            <block type="math_constant"></block>
            <block type="math_number_property"></block>
            <block type="math_round"></block>
            <block type="math_on_list"></block>
            <block type="math_modulo"></block>
            <block type="math_constrain"></block>
            <block type="math_random_int"></block>
            <block type="math_random_float"></block>
        </category>
        
        <!-- 文本 -->
        <category name="Text" colour="#5CA68D">
            <block type="text"></block>
            <block type="text_join"></block>
            <block type="text_append"></block>
            <block type="text_length"></block>
            <block type="text_isEmpty"></block>
            <block type="text_indexOf"></block>
            <block type="text_charAt"></block>
            <block type="text_getSubstring"></block>
            <block type="text_changeCase"></block>
            <block type="text_trim"></block>
            <block type="text_print"></block>
        </category>
        
        <!-- 列表 -->
        <category name="Lists" colour="#745CA6">
            <block type="lists_create_with"></block>
            <block type="lists_create_empty"></block>
            <block type="lists_repeat"></block>
            <block type="lists_length"></block>
            <block type="lists_isEmpty"></block>
            <block type="lists_indexOf"></block>
            <block type="lists_getIndex"></block>
            <block type="lists_setIndex"></block>
            <block type="lists_getSublist"></block>
            <block type="lists_split"></block>
            <block type="lists_sort"></block>
            <block type="lists_reverse"></block>
        </category>
        
        <!-- 变量 -->
        <category name="Variables" colour="#A65C81" custom="VARIABLE"></category>
        
        <!-- 函数 -->
        <category name="Functions" colour="#9A5CA6" custom="PROCEDURE"></category>
        
        <!-- ESP32 GPIO -->
        <category name="ESP32 GPIO" colour="#FF6B35">
            <block type="esp32_digital_write"></block>
            <block type="esp32_digital_read"></block>
            <block type="esp32_analog_read"></block>
            <block type="esp32_analog_write"></block>
            <block type="esp32_pin_mode"></block>
        </category>
        
        <!-- ESP32 WiFi -->
        <category name="ESP32 WiFi" colour="#4ECDC4">
            <block type="esp32_wifi_connect"></block>
            <block type="esp32_wifi_disconnect"></block>
            <block type="esp32_wifi_status"></block>
            <block type="esp32_wifi_scan"></block>
        </category>
        
        <!-- ESP32 传感器 -->
        <category name="ESP32 Sensors" colour="#45B7D1">
            <block type="esp32_dht_read"></block>
            <block type="esp32_ultrasonic"></block>
            <block type="esp32_touch_read"></block>
            <block type="esp32_hall_read"></block>
        </category>
        
        <!-- ESP32 显示 -->
        <category name="ESP32 Display" colour="#F7DC6F">
            <block type="esp32_oled_init"></block>
            <block type="esp32_oled_text"></block>
            <block type="esp32_oled_clear"></block>
            <block type="esp32_oled_show"></block>
        </category>
        
        <!-- ESP32 通信 -->
        <category name="ESP32 Communication" colour="#BB8FCE">
            <block type="esp32_serial_print"></block>
            <block type="esp32_serial_read"></block>
            <block type="esp32_i2c_write"></block>
            <block type="esp32_i2c_read"></block>
        </category>
        
        <!-- ESP32 时间 -->
        <category name="ESP32 Time" colour="#85C1E9">
            <block type="esp32_delay"></block>
            <block type="esp32_delay_microseconds"></block>
            <block type="esp32_millis"></block>
            <block type="esp32_micros"></block>
        </category>
    </xml>

    <script>
        // 初始化Blockly工作区
        var workspace = Blockly.inject('blocklyDiv', {
            toolbox: document.getElementById('toolbox'),
            grid: {
                spacing: 20,
                length: 3,
                colour: '#ccc',
                snap: true
            },
            zoom: {
                controls: true,
                wheel: true,
                startScale: 1.0,
                maxScale: 3,
                minScale: 0.3,
                scaleSpeed: 1.2
            },
            trashcan: true,
            scrollbars: true,
            sounds: false,
            oneBasedIndex: false
        });

        // 与Qt应用程序通信的接口
        var qtInterface = {
            // 发送消息到Qt应用程序
            sendMessage: function(type, data) {
                if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                    var message = {
                        type: type,
                        data: data || {}
                    };
                    
                    // 通过WebChannel发送消息
                    if (window.blocklyManager) {
                        window.blocklyManager.handleJavaScriptMessage(message);
                    }
                }
            },
            
            // 工作区变化处理
            onWorkspaceChange: function() {
                var xml = Blockly.Xml.workspaceToDom(workspace);
                var xmlText = Blockly.Xml.domToText(xml);
                
                var code = Blockly.Python.workspaceToCode(workspace);
                
                qtInterface.sendMessage('workspaceChanged', {
                    xml: xmlText
                });
                
                qtInterface.sendMessage('codeChanged', {
                    code: code
                });
                
                // 更新撤销/重做状态
                qtInterface.sendMessage('undoRedoStateChanged', {
                    canUndo: workspace.undoStack_.length > 0,
                    canRedo: workspace.redoStack_.length > 0
                });
            }
        };

        // 监听工作区变化
        workspace.addChangeListener(qtInterface.onWorkspaceChange);

        // 全局函数，供Qt调用
        window.blocklyInterface = {
            getWorkspaceXml: function() {
                var xml = Blockly.Xml.workspaceToDom(workspace);
                return Blockly.Xml.domToText(xml);
            },
            
            setWorkspaceXml: function(xmlText) {
                try {
                    var xml = Blockly.Xml.textToDom(xmlText);
                    Blockly.Xml.domToWorkspace(xml, workspace);
                } catch (e) {
                    console.error('Failed to load workspace XML:', e);
                }
            },
            
            getGeneratedCode: function() {
                return Blockly.Python.workspaceToCode(workspace);
            },
            
            clearWorkspace: function() {
                workspace.clear();
            },
            
            undo: function() {
                workspace.undo(false);
            },
            
            redo: function() {
                workspace.undo(true);
            },
            
            canUndo: function() {
                return workspace.undoStack_.length > 0;
            },
            
            canRedo: function() {
                return workspace.redoStack_.length > 0;
            }
        };

        // 页面加载完成后通知Qt
        window.addEventListener('load', function() {
            qtInterface.sendMessage('blocklyReady', {});
        });
    </script>
</body>
</html>
