// ESP32自定义积木块定义

// GPIO相关积木块
Blockly.Blocks['esp32_digital_write'] = {
  init: function() {
    this.appendValueInput("PIN")
        .setCheck("Number")
        .appendField("digital write pin");
    this.appendValueInput("VALUE")
        .setCheck("Boolean")
        .appendField("value");
    this.setPreviousStatement(true, null);
    this.setNextStatement(true, null);
    this.setColour(230);
    this.setTooltip("Write digital value to pin");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_digital_write'] = function(block) {
  var value_pin = Blockly.Python.valueToCode(block, 'PIN', Blockly.Python.ORDER_ATOMIC);
  var value_value = Blockly.Python.valueToCode(block, 'VALUE', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'from machine import Pin\n';
  code += 'pin' + value_pin + ' = Pin(' + value_pin + ', Pin.OUT)\n';
  code += 'pin' + value_pin + '.value(' + (value_value === 'True' ? '1' : '0') + ')\n';
  return code;
};

Blockly.Blocks['esp32_digital_read'] = {
  init: function() {
    this.appendValueInput("PIN")
        .setCheck("Number")
        .appendField("digital read pin");
    this.setOutput(true, "Boolean");
    this.setColour(230);
    this.setTooltip("Read digital value from pin");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_digital_read'] = function(block) {
  var value_pin = Blockly.Python.valueToCode(block, 'PIN', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'Pin(' + value_pin + ', Pin.IN).value()';
  return [code, Blockly.Python.ORDER_NONE];
};

Blockly.Blocks['esp32_analog_read'] = {
  init: function() {
    this.appendValueInput("PIN")
        .setCheck("Number")
        .appendField("analog read pin");
    this.setOutput(true, "Number");
    this.setColour(230);
    this.setTooltip("Read analog value from pin");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_analog_read'] = function(block) {
  var value_pin = Blockly.Python.valueToCode(block, 'PIN', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'from machine import ADC\n';
  code += 'ADC(Pin(' + value_pin + ')).read()\n';
  return [code, Blockly.Python.ORDER_NONE];
};

// WiFi相关积木块
Blockly.Blocks['esp32_wifi_connect'] = {
  init: function() {
    this.appendValueInput("SSID")
        .setCheck("String")
        .appendField("WiFi connect to");
    this.appendValueInput("PASSWORD")
        .setCheck("String")
        .appendField("password");
    this.setPreviousStatement(true, null);
    this.setNextStatement(true, null);
    this.setColour(160);
    this.setTooltip("Connect to WiFi network");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_wifi_connect'] = function(block) {
  var value_ssid = Blockly.Python.valueToCode(block, 'SSID', Blockly.Python.ORDER_ATOMIC);
  var value_password = Blockly.Python.valueToCode(block, 'PASSWORD', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'import network\n';
  code += 'wlan = network.WLAN(network.STA_IF)\n';
  code += 'wlan.active(True)\n';
  code += 'wlan.connect(' + value_ssid + ', ' + value_password + ')\n';
  code += 'while not wlan.isconnected():\n';
  code += '    pass\n';
  return code;
};

// 传感器相关积木块
Blockly.Blocks['esp32_dht_read'] = {
  init: function() {
    this.appendValueInput("PIN")
        .setCheck("Number")
        .appendField("DHT22 read pin");
    this.appendDummyInput()
        .appendField("get")
        .appendField(new Blockly.FieldDropdown([["temperature","temperature"], ["humidity","humidity"]]), "TYPE");
    this.setOutput(true, "Number");
    this.setColour(120);
    this.setTooltip("Read temperature or humidity from DHT22 sensor");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_dht_read'] = function(block) {
  var value_pin = Blockly.Python.valueToCode(block, 'PIN', Blockly.Python.ORDER_ATOMIC);
  var dropdown_type = block.getFieldValue('TYPE');
  
  var code = 'import dht\n';
  code += 'from machine import Pin\n';
  code += 'sensor = dht.DHT22(Pin(' + value_pin + '))\n';
  code += 'sensor.measure()\n';
  
  if (dropdown_type === 'temperature') {
    code += 'sensor.temperature()';
  } else {
    code += 'sensor.humidity()';
  }
  
  return [code, Blockly.Python.ORDER_NONE];
};

// 显示相关积木块
Blockly.Blocks['esp32_oled_init'] = {
  init: function() {
    this.appendValueInput("SDA")
        .setCheck("Number")
        .appendField("OLED init SDA pin");
    this.appendValueInput("SCL")
        .setCheck("Number")
        .appendField("SCL pin");
    this.setPreviousStatement(true, null);
    this.setNextStatement(true, null);
    this.setColour(60);
    this.setTooltip("Initialize OLED display");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_oled_init'] = function(block) {
  var value_sda = Blockly.Python.valueToCode(block, 'SDA', Blockly.Python.ORDER_ATOMIC);
  var value_scl = Blockly.Python.valueToCode(block, 'SCL', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'from machine import Pin, I2C\n';
  code += 'import ssd1306\n';
  code += 'i2c = I2C(-1, scl=Pin(' + value_scl + '), sda=Pin(' + value_sda + '))\n';
  code += 'oled = ssd1306.SSD1306_I2C(128, 64, i2c)\n';
  return code;
};

Blockly.Blocks['esp32_oled_text'] = {
  init: function() {
    this.appendValueInput("TEXT")
        .setCheck("String")
        .appendField("OLED show text");
    this.appendValueInput("X")
        .setCheck("Number")
        .appendField("at x");
    this.appendValueInput("Y")
        .setCheck("Number")
        .appendField("y");
    this.setPreviousStatement(true, null);
    this.setNextStatement(true, null);
    this.setColour(60);
    this.setTooltip("Display text on OLED");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_oled_text'] = function(block) {
  var value_text = Blockly.Python.valueToCode(block, 'TEXT', Blockly.Python.ORDER_ATOMIC);
  var value_x = Blockly.Python.valueToCode(block, 'X', Blockly.Python.ORDER_ATOMIC);
  var value_y = Blockly.Python.valueToCode(block, 'Y', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'oled.text(' + value_text + ', ' + value_x + ', ' + value_y + ')\n';
  return code;
};

// 时间相关积木块
Blockly.Blocks['esp32_delay'] = {
  init: function() {
    this.appendValueInput("TIME")
        .setCheck("Number")
        .appendField("delay");
    this.appendDummyInput()
        .appendField("milliseconds");
    this.setPreviousStatement(true, null);
    this.setNextStatement(true, null);
    this.setColour(290);
    this.setTooltip("Delay for specified milliseconds");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_delay'] = function(block) {
  var value_time = Blockly.Python.valueToCode(block, 'TIME', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'import time\n';
  code += 'time.sleep_ms(' + value_time + ')\n';
  return code;
};

// 串口相关积木块
Blockly.Blocks['esp32_serial_print'] = {
  init: function() {
    this.appendValueInput("TEXT")
        .setCheck("String")
        .appendField("print");
    this.setPreviousStatement(true, null);
    this.setNextStatement(true, null);
    this.setColour(160);
    this.setTooltip("Print text to serial console");
    this.setHelpUrl("");
  }
};

Blockly.Python['esp32_serial_print'] = function(block) {
  var value_text = Blockly.Python.valueToCode(block, 'TEXT', Blockly.Python.ORDER_ATOMIC);
  
  var code = 'print(' + value_text + ')\n';
  return code;
};
