#include "projectmanager.h"
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDateTime>
#include <QJsonArray>
#include <QDebug>

ProjectManager::ProjectManager(QObject *parent)
    : QObject(parent)
    , m_isModified(false)
    , m_hasOpenProject(false)
{
}

ProjectManager::~ProjectManager()
{
}

bool ProjectManager::createNewProject(const QString &name, const QString &description, const QString &author)
{
    if (name.isEmpty()) {
        emit errorOccurred("Project name cannot be empty");
        return false;
    }

    // 关闭当前项目
    if (m_hasOpenProject) {
        closeProject();
    }

    // 初始化新项目
    initializeDefaultProject();
    
    m_currentProject.info.name = name;
    m_currentProject.info.description = description;
    m_currentProject.info.author = author;
    m_currentProject.info.version = "1.0.0";
    m_currentProject.info.createDate = getCurrentTimeString();
    m_currentProject.info.modifyDate = m_currentProject.info.createDate;

    m_hasOpenProject = true;
    m_isModified = true;
    m_currentFilePath.clear();

    emit projectCreated(m_currentProject.info);
    emit projectModifiedChanged(m_isModified);

    return true;
}

bool ProjectManager::saveProject(const QString &filePath)
{
    if (!m_hasOpenProject) {
        emit errorOccurred("No project to save");
        return false;
    }

    QString saveFilePath = filePath;
    if (saveFilePath.isEmpty()) {
        if (m_currentFilePath.isEmpty()) {
            emit errorOccurred("No file path specified for new project");
            return false;
        }
        saveFilePath = m_currentFilePath;
    }

    return saveProjectAs(saveFilePath);
}

bool ProjectManager::saveProjectAs(const QString &filePath)
{
    if (!m_hasOpenProject) {
        emit errorOccurred("No project to save");
        return false;
    }

    if (filePath.isEmpty()) {
        emit errorOccurred("File path cannot be empty");
        return false;
    }

    // 更新项目信息
    m_currentProject.info.modifyDate = getCurrentTimeString();
    m_currentProject.info.filePath = filePath;

    // 生成JSON
    QJsonDocument doc = generateProjectJson();
    
    // 保存文件
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit errorOccurred("Failed to open file for writing: " + file.errorString());
        return false;
    }

    file.write(doc.toJson());
    file.close();

    m_currentFilePath = filePath;
    clearModifiedFlag();

    emit projectSaved(filePath);
    return true;
}

bool ProjectManager::openProject(const QString &filePath)
{
    if (filePath.isEmpty()) {
        emit errorOccurred("File path cannot be empty");
        return false;
    }

    if (!QFile::exists(filePath)) {
        emit errorOccurred("Project file does not exist: " + filePath);
        return false;
    }

    // 验证项目文件
    if (!validateProjectFile(filePath)) {
        emit errorOccurred("Invalid project file format");
        return false;
    }

    // 关闭当前项目
    if (m_hasOpenProject) {
        closeProject();
    }

    // 读取文件
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit errorOccurred("Failed to open project file: " + file.errorString());
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    // 解析JSON
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        emit errorOccurred("Failed to parse project file: " + error.errorString());
        return false;
    }

    if (!parseProjectJson(doc)) {
        emit errorOccurred("Failed to load project data");
        return false;
    }

    m_currentFilePath = filePath;
    m_hasOpenProject = true;
    clearModifiedFlag();

    emit projectOpened(m_currentProject);
    return true;
}

bool ProjectManager::closeProject()
{
    if (!m_hasOpenProject) {
        return true;
    }

    initializeDefaultProject();
    m_hasOpenProject = false;
    m_currentFilePath.clear();
    clearModifiedFlag();

    emit projectClosed();
    return true;
}

bool ProjectManager::exportCode(const QString &filePath, const QString &code)
{
    if (filePath.isEmpty() || code.isEmpty()) {
        emit errorOccurred("File path or code cannot be empty");
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit errorOccurred("Failed to open file for writing: " + file.errorString());
        return false;
    }

    QTextStream out(&file);
    out << code;
    file.close();

    emit codeExported(filePath);
    return true;
}

ProjectManager::ProjectInfo ProjectManager::getCurrentProjectInfo() const
{
    return m_currentProject.info;
}

ProjectManager::ProjectData ProjectManager::getCurrentProjectData() const
{
    return m_currentProject;
}

void ProjectManager::setWorkspaceXml(const QString &xml)
{
    if (m_currentProject.workspaceXml != xml) {
        m_currentProject.workspaceXml = xml;
        markProjectModified();
    }
}

QString ProjectManager::getWorkspaceXml() const
{
    return m_currentProject.workspaceXml;
}

void ProjectManager::setGeneratedCode(const QString &code)
{
    if (m_currentProject.generatedCode != code) {
        m_currentProject.generatedCode = code;
        markProjectModified();
    }
}

QString ProjectManager::getGeneratedCode() const
{
    return m_currentProject.generatedCode;
}

void ProjectManager::setProjectSettings(const QJsonObject &settings)
{
    if (m_currentProject.settings != settings) {
        m_currentProject.settings = settings;
        markProjectModified();
    }
}

QJsonObject ProjectManager::getProjectSettings() const
{
    return m_currentProject.settings;
}

bool ProjectManager::isProjectModified() const
{
    return m_isModified;
}

bool ProjectManager::hasOpenProject() const
{
    return m_hasOpenProject;
}

QString ProjectManager::getCurrentProjectPath() const
{
    return m_currentFilePath;
}

QString ProjectManager::getProjectFileExtension()
{
    return "esp32proj";
}

QString ProjectManager::getProjectFileFilter()
{
    return "ESP32 Blockly Project Files (*.esp32proj)";
}

bool ProjectManager::validateProjectFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    if (fileInfo.suffix().toLower() != getProjectFileExtension()) {
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    file.close();

    if (error.error != QJsonParseError::NoError) {
        return false;
    }

    QJsonObject root = doc.object();
    return root.contains("project_info") && root.contains("workspace_xml");
}

void ProjectManager::markProjectModified()
{
    if (!m_isModified) {
        m_isModified = true;
        emit projectModifiedChanged(m_isModified);
    }
}

void ProjectManager::clearModifiedFlag()
{
    if (m_isModified) {
        m_isModified = false;
        emit projectModifiedChanged(m_isModified);
    }
}

QJsonDocument ProjectManager::generateProjectJson() const
{
    QJsonObject root;
    
    // 项目信息
    QJsonObject projectInfo;
    projectInfo["name"] = m_currentProject.info.name;
    projectInfo["description"] = m_currentProject.info.description;
    projectInfo["author"] = m_currentProject.info.author;
    projectInfo["version"] = m_currentProject.info.version;
    projectInfo["create_date"] = m_currentProject.info.createDate;
    projectInfo["modify_date"] = m_currentProject.info.modifyDate;
    projectInfo["file_path"] = m_currentProject.info.filePath;
    
    root["project_info"] = projectInfo;
    root["workspace_xml"] = m_currentProject.workspaceXml;
    root["generated_code"] = m_currentProject.generatedCode;
    root["settings"] = m_currentProject.settings;
    
    // 添加文件格式版本
    root["format_version"] = "1.0";
    
    return QJsonDocument(root);
}

bool ProjectManager::parseProjectJson(const QJsonDocument &json)
{
    QJsonObject root = json.object();
    
    if (!root.contains("project_info") || !root.contains("workspace_xml")) {
        return false;
    }
    
    // 解析项目信息
    QJsonObject projectInfo = root["project_info"].toObject();
    m_currentProject.info.name = projectInfo["name"].toString();
    m_currentProject.info.description = projectInfo["description"].toString();
    m_currentProject.info.author = projectInfo["author"].toString();
    m_currentProject.info.version = projectInfo["version"].toString();
    m_currentProject.info.createDate = projectInfo["create_date"].toString();
    m_currentProject.info.modifyDate = projectInfo["modify_date"].toString();
    m_currentProject.info.filePath = projectInfo["file_path"].toString();
    
    // 解析其他数据
    m_currentProject.workspaceXml = root["workspace_xml"].toString();
    m_currentProject.generatedCode = root["generated_code"].toString();
    m_currentProject.settings = root["settings"].toObject();
    
    return true;
}

void ProjectManager::initializeDefaultProject()
{
    m_currentProject = ProjectData();
    m_currentProject.info.name = "Untitled Project";
    m_currentProject.info.description = "";
    m_currentProject.info.author = "";
    m_currentProject.info.version = "1.0.0";
    m_currentProject.workspaceXml = "";
    m_currentProject.generatedCode = "";
    m_currentProject.settings = QJsonObject();
}

QString ProjectManager::getCurrentTimeString() const
{
    return QDateTime::currentDateTime().toString(Qt::ISODate);
}
